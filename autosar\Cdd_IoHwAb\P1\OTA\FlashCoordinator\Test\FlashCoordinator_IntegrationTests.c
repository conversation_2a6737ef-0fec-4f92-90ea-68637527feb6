/*****************************************************************************
*
*   All Contents@CopyRight Of Neusoft Corporation
*
*   All rights reserved.
*
*   Filename: FlashCoordinator_IntegrationTests.c   
*
*   Description: Integration test implementations for FlashCoordinator
*
*   Revision History:
*
*   Date            Author          Description
*   ----------      ----------      -----------------------------------------
*   2025-01-31      AI-Assistant    Initial created for integration tests
*
*****************************************************************************/

/***************************************************************************/
/*---------------------------------Includes--------------------------------*/
/***************************************************************************/
#include "FlashCoordinator_Test.h"

/***************************************************************************/
/*------------------Integration Test Implementations-----------------------*/
/***************************************************************************/

/**
 * @brief Test full system integration
 */
FlashCoord_TestResultType FlashCoord_Test_Integration_FullSystem(void)
{
    FlashCoord_ResultType result;
    Sota_FL_ResultType otaResult;
    
    /* Initialize all modules */
    result = FlashCoord_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "FlashCoordinator initialization failed");
    
    result = FlashCoord_AutosarIntegration_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "AUTOSAR integration initialization failed");
    
    result = FlashCoord_ErrorHandling_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Error handling initialization failed");
    
    result = FlashCoord_PFlashOptimization_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "PFlash optimization initialization failed");
    
    result = FlashCoord_Logging_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Logging initialization failed");
    
    otaResult = SOTA_FL_SafeAccess_Init();
    FLASH_COORD_TEST_ASSERT(otaResult == SOTA_FL_OK, "OTA safe access initialization failed");
    
    /* Test coordinated operation */
    otaResult = SOTA_FL_SafeAccess_ReadNvmData(FLASH_COORD_TEST_DFLASH_ADDR, NULL);
    /* Note: This should fail due to NULL pointer, but coordination should work */
    FLASH_COORD_TEST_ASSERT(otaResult == SOTA_FL_FAILED, "Expected failure for NULL pointer");
    
    /* Test valid operation */
    uint32 testData;
    otaResult = SOTA_FL_SafeAccess_ReadNvmData(FLASH_COORD_TEST_DFLASH_ADDR, &testData);
    FLASH_COORD_TEST_ASSERT(otaResult == SOTA_FL_OK, "Valid OTA read operation failed");
    
    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test OTA-AUTOSAR coordination
 */
FlashCoord_TestResultType FlashCoord_Test_Integration_OTA_AUTOSAR_Coordination(void)
{
    FlashCoord_ResultType coordResult;
    Sota_FL_ResultType otaResult;
    uint32 testData;
    
    /* Initialize system */
    FlashCoord_Init();
    FlashCoord_AutosarIntegration_Init();
    SOTA_FL_SafeAccess_Init();
    
    /* Simulate AUTOSAR module taking DFlash access */
    coordResult = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_NVM,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(coordResult == FLASH_COORD_OK, "AUTOSAR DFlash access failed");
    
    /* OTA should be blocked or wait */
    otaResult = SOTA_FL_SafeAccess_ReadNvmData(FLASH_COORD_TEST_DFLASH_ADDR, &testData);
    /* This might succeed if coordination allows it, or fail if blocked */
    /* The important thing is that it doesn't cause conflicts */
    
    /* Release AUTOSAR access */
    coordResult = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_NVM);
    FLASH_COORD_TEST_ASSERT(coordResult == FLASH_COORD_OK, "AUTOSAR access release failed");
    
    /* Now OTA should succeed */
    otaResult = SOTA_FL_SafeAccess_ReadNvmData(FLASH_COORD_TEST_DFLASH_ADDR, &testData);
    FLASH_COORD_TEST_ASSERT(otaResult == SOTA_FL_OK, "OTA access after AUTOSAR release failed");
    
    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test DFlash conflict resolution
 */
FlashCoord_TestResultType FlashCoord_Test_Integration_DFlash_Conflict_Resolution(void)
{
    FlashCoord_ResultType result1, result2;
    uint32 testData;
    
    /* Initialize system */
    FlashCoord_Init();
    FlashCoord_ErrorHandling_Init();
    SOTA_FL_SafeAccess_Init();
    
    /* Test scenario: Multiple owners trying to access same DFlash region */
    
    /* Owner 1 (OTA) requests access */
    result1 = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_OTA,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(result1 == FLASH_COORD_OK, "First DFlash access request failed");
    
    /* Owner 2 (NVM) requests access to overlapping region */
    result2 = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_NVM,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR + 512u, /* Overlapping region */
        1024u,
        1000u /* Shorter timeout */
    );
    FLASH_COORD_TEST_ASSERT(result2 != FLASH_COORD_OK, "Conflicting DFlash access should be denied");
    
    /* Verify first owner still has access */
    FlashCoord_OwnerType currentOwner = FlashCoord_GetCurrentOwner();
    FLASH_COORD_TEST_ASSERT(currentOwner == FLASH_COORD_OWNER_OTA, "Current owner should be OTA");
    
    /* Release first access */
    result1 = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_OTA);
    FLASH_COORD_TEST_ASSERT(result1 == FLASH_COORD_OK, "First access release failed");
    
    /* Now second owner should be able to get access */
    result2 = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_NVM,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR + 512u,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(result2 == FLASH_COORD_OK, "Second access after release failed");
    
    /* Clean up */
    result2 = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_NVM);
    FLASH_COORD_TEST_ASSERT(result2 == FLASH_COORD_OK, "Second access release failed");
    
    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test PFlash parallel access
 */
FlashCoord_TestResultType FlashCoord_Test_Integration_PFlash_Parallel_Access(void)
{
    FlashCoord_ResultType resultA, resultB;
    boolean canRunInParallel;
    
    /* Initialize system */
    FlashCoord_Init();
    FlashCoord_PFlashOptimization_Init();
    
    /* Configure AB swap */
    FlashCoord_ResultType configResult = FlashCoord_PFlashOptimization_ConfigureABSwap(
        FLASH_COORD_PFLASH_BANK_A,  /* Active bank */
        FLASH_COORD_PFLASH_BANK_B   /* OTA bank */
    );
    FLASH_COORD_TEST_ASSERT(configResult == FLASH_COORD_OK, "AB swap configuration failed");
    
    /* Test parallel access to different banks */
    
    /* AUTOSAR accesses Bank A (active bank) */
    resultA = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_SYSTEM,
        FLASH_COORD_MEM_TYPE_PFLASH_A,
        FLASH_COORD_TEST_PFLASH_A_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(resultA == FLASH_COORD_OK, "PFlash Bank A access failed");
    
    /* OTA accesses Bank B (OTA bank) - should succeed in parallel */
    resultB = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_OTA,
        FLASH_COORD_MEM_TYPE_PFLASH_B,
        FLASH_COORD_TEST_PFLASH_B_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(resultB == FLASH_COORD_OK, "PFlash Bank B parallel access failed");
    
    /* Verify parallel access capability */
    canRunInParallel = FlashCoord_PFlashOptimization_CanRunInParallel(
        FLASH_COORD_TEST_PFLASH_A_ADDR,
        FLASH_COORD_OWNER_SYSTEM
    );
    FLASH_COORD_TEST_ASSERT(canRunInParallel == TRUE, "Bank A should allow parallel access");
    
    canRunInParallel = FlashCoord_PFlashOptimization_CanRunInParallel(
        FLASH_COORD_TEST_PFLASH_B_ADDR,
        FLASH_COORD_OWNER_OTA
    );
    FLASH_COORD_TEST_ASSERT(canRunInParallel == TRUE, "Bank B should allow parallel access");
    
    /* Test cross-bank access restriction */
    FlashCoord_ResultType crossResult = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_OTA,
        FLASH_COORD_MEM_TYPE_PFLASH_A,  /* OTA trying to access active bank */
        FLASH_COORD_TEST_PFLASH_A_ADDR + 0x10000u,
        1024u,
        1000u
    );
    /* This should be coordinated or restricted based on AB swap policy */
    
    /* Clean up */
    resultA = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_SYSTEM);
    FLASH_COORD_TEST_ASSERT(resultA == FLASH_COORD_OK, "Bank A access release failed");
    
    resultB = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_OTA);
    FLASH_COORD_TEST_ASSERT(resultB == FLASH_COORD_OK, "Bank B access release failed");
    
    if (crossResult == FLASH_COORD_OK)
    {
        FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_OTA);
    }
    
    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test error recovery scenarios
 */
FlashCoord_TestResultType FlashCoord_Test_Integration_Error_Recovery_Scenarios(void)
{
    FlashCoord_ResultType result;
    FlashCoord_ErrorInfoType errorInfo;
    
    /* Initialize system */
    FlashCoord_Init();
    FlashCoord_ErrorHandling_Init();
    FlashCoord_Logging_Init();
    
    /* Scenario 1: Timeout recovery */
    
    /* Request access */
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_OTA,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Initial access request failed");
    
    /* Start timeout monitoring */
    result = FlashCoord_ErrorHandling_StartTimeoutMonitoring(FLASH_COORD_OWNER_OTA, 2000u);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Timeout monitoring start failed");
    
    /* Simulate timeout by not releasing access */
    /* In real scenario, MainFunction would detect timeout */
    
    /* Create timeout error */
    errorInfo.errorType = FLASH_COORD_ERROR_TYPE_TIMEOUT;
    errorInfo.severity = FLASH_COORD_ERROR_SEVERITY_CRITICAL;
    errorInfo.owner = FLASH_COORD_OWNER_OTA;
    errorInfo.errorCode = 2500u; /* Elapsed time */
    errorInfo.timestamp = 0u;
    errorInfo.address = FLASH_COORD_TEST_DFLASH_ADDR;
    errorInfo.length = 1024u;
    errorInfo.description = "Test timeout scenario";
    
    /* Report error and trigger recovery */
    result = FlashCoord_ErrorHandling_ReportError(&errorInfo);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Error reporting failed");
    
    /* Test force release recovery */
    result = FlashCoord_ErrorHandling_ForceRelease(FLASH_COORD_OWNER_OTA);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Force release recovery failed");
    
    /* Verify access is released */
    FlashCoord_OwnerType currentOwner = FlashCoord_GetCurrentOwner();
    FLASH_COORD_TEST_ASSERT(currentOwner == FLASH_COORD_OWNER_NONE, "Access should be released after recovery");
    
    /* Stop timeout monitoring */
    result = FlashCoord_ErrorHandling_StopTimeoutMonitoring(FLASH_COORD_OWNER_OTA);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Timeout monitoring stop failed");
    
    return FLASH_COORD_TEST_PASS;
}
