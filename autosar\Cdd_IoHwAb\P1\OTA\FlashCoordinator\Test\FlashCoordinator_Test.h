/*****************************************************************************
*
*   All Contents@CopyRight Of Neusoft Corporation
*
*   All rights reserved.
*
*   Filename: FlashCoordinator_Test.h   
*
*   Description: Test framework for FlashCoordinator system
*                Provides comprehensive unit tests and integration tests
*                to verify Flash coordination and OTA refactoring
*
*   Revision History:
*
*   Date            Author          Description
*   ----------      ----------      -----------------------------------------
*   2025-01-31      AI-Assistant    Initial created for testing framework
*
*****************************************************************************/

#ifndef FLASH_COORDINATOR_TEST_H
#define FLASH_COORDINATOR_TEST_H

/***************************************************************************/
/*---------------------------------Includes--------------------------------*/
/***************************************************************************/
#include "Std_Types.h"
#include "FlashCoordinator.h"
#include "FlashCoordinator_ErrorHandling.h"
#include "FlashCoordinator_AutosarIntegration.h"
#include "FlashCoordinator_PFlashOptimization.h"
#include "FlashCoordinator_Logging.h"
#include "SOTA_FL_SafeAccess.h"

/***************************************************************************/
/*----------------------------------Macros---------------------------------*/
/***************************************************************************/

/* Test configuration */
#define FLASH_COORD_TEST_ENABLED                  STD_ON
#define FLASH_COORD_UNIT_TEST_ENABLED             STD_ON
#define FLASH_COORD_INTEGRATION_TEST_ENABLED      STD_ON
#define FLASH_COORD_STRESS_TEST_ENABLED           STD_ON

/* Test parameters */
#define FLASH_COORD_TEST_MAX_ITERATIONS           1000u
#define FLASH_COORD_TEST_TIMEOUT_MS               10000u
#define FLASH_COORD_TEST_STRESS_DURATION_MS       60000u

/* Test addresses */
#define FLASH_COORD_TEST_DFLASH_ADDR              0xAF084000uL
#define FLASH_COORD_TEST_PFLASH_A_ADDR            0xA0100000uL
#define FLASH_COORD_TEST_PFLASH_B_ADDR            0xA1100000uL

/* Test data patterns */
#define FLASH_COORD_TEST_DATA_PATTERN_1           0xAAAAAAAAuL
#define FLASH_COORD_TEST_DATA_PATTERN_2           0x55555555uL
#define FLASH_COORD_TEST_DATA_PATTERN_3           0x12345678uL

/***************************************************************************/
/*----------------------------Type Definitions-----------------------------*/
/***************************************************************************/

/** Test result types */
typedef enum
{
    FLASH_COORD_TEST_PASS = 0u,             /**< Test passed */
    FLASH_COORD_TEST_FAIL,                  /**< Test failed */
    FLASH_COORD_TEST_SKIP,                  /**< Test skipped */
    FLASH_COORD_TEST_ERROR                  /**< Test error */
} FlashCoord_TestResultType;

/** Test case structure */
typedef struct
{
    const char* testName;                   /**< Test case name */
    FlashCoord_TestResultType (*testFunction)(void); /**< Test function pointer */
    const char* description;                /**< Test description */
    boolean enabled;                        /**< Test enabled flag */
} FlashCoord_TestCaseType;

/** Test suite structure */
typedef struct
{
    const char* suiteName;                  /**< Test suite name */
    const FlashCoord_TestCaseType* testCases; /**< Array of test cases */
    uint32 testCaseCount;                   /**< Number of test cases */
    uint32 passCount;                       /**< Number of passed tests */
    uint32 failCount;                       /**< Number of failed tests */
    uint32 skipCount;                       /**< Number of skipped tests */
    uint32 errorCount;                      /**< Number of error tests */
} FlashCoord_TestSuiteType;

/** Test statistics */
typedef struct
{
    uint32 totalTests;                      /**< Total number of tests */
    uint32 totalPassed;                     /**< Total passed tests */
    uint32 totalFailed;                     /**< Total failed tests */
    uint32 totalSkipped;                    /**< Total skipped tests */
    uint32 totalErrors;                     /**< Total error tests */
    uint32 executionTimeMs;                 /**< Total execution time */
} FlashCoord_TestStatisticsType;

/***************************************************************************/
/*--------------------External Function Declarations-----------------------*/
/***************************************************************************/

/**
 * @brief Initialize test framework
 * 
 * @details This function initializes the test framework and
 *          prepares for test execution.
 * 
 * @param[in] None
 * @return FlashCoord_ResultType
 * @retval FLASH_COORD_OK           Initialization successful
 * @retval FLASH_COORD_ERROR        Initialization failed
 */
FlashCoord_ResultType FlashCoord_Test_Init(void);

/**
 * @brief Run all unit tests
 * 
 * @details This function executes all unit tests for the
 *          FlashCoordinator system.
 * 
 * @param[in] None
 * @return FlashCoord_TestResultType
 * @retval FLASH_COORD_TEST_PASS    All tests passed
 * @retval FLASH_COORD_TEST_FAIL    Some tests failed
 */
FlashCoord_TestResultType FlashCoord_Test_RunUnitTests(void);

/**
 * @brief Run all integration tests
 * 
 * @details This function executes all integration tests for the
 *          FlashCoordinator system and OTA integration.
 * 
 * @param[in] None
 * @return FlashCoord_TestResultType
 * @retval FLASH_COORD_TEST_PASS    All tests passed
 * @retval FLASH_COORD_TEST_FAIL    Some tests failed
 */
FlashCoord_TestResultType FlashCoord_Test_RunIntegrationTests(void);

/**
 * @brief Run stress tests
 * 
 * @details This function executes stress tests to verify
 *          system behavior under high load conditions.
 * 
 * @param[in] None
 * @return FlashCoord_TestResultType
 * @retval FLASH_COORD_TEST_PASS    All tests passed
 * @retval FLASH_COORD_TEST_FAIL    Some tests failed
 */
FlashCoord_TestResultType FlashCoord_Test_RunStressTests(void);

/**
 * @brief Run all tests
 * 
 * @details This function executes all available tests
 *          (unit, integration, and stress tests).
 * 
 * @param[in] None
 * @return FlashCoord_TestResultType
 * @retval FLASH_COORD_TEST_PASS    All tests passed
 * @retval FLASH_COORD_TEST_FAIL    Some tests failed
 */
FlashCoord_TestResultType FlashCoord_Test_RunAllTests(void);

/**
 * @brief Get test statistics
 * 
 * @details This function retrieves test execution statistics.
 * 
 * @param[out] statistics           Pointer to statistics structure
 * @return FlashCoord_ResultType
 * @retval FLASH_COORD_OK           Statistics retrieved
 * @retval FLASH_COORD_INVALID_PARAM Invalid parameter
 */
FlashCoord_ResultType FlashCoord_Test_GetStatistics(
    FlashCoord_TestStatisticsType* statistics
);

/**
 * @brief Reset test statistics
 * 
 * @details This function resets all test statistics.
 * 
 * @param[in] None
 * @return FlashCoord_ResultType
 * @retval FLASH_COORD_OK           Statistics reset
 */
FlashCoord_ResultType FlashCoord_Test_ResetStatistics(void);

/***************************************************************************/
/*-----------------------Unit Test Function Declarations------------------*/
/***************************************************************************/

/* FlashCoordinator core tests */
FlashCoord_TestResultType FlashCoord_Test_Init_Basic(void);
FlashCoord_TestResultType FlashCoord_Test_RequestAccess_DFlash(void);
FlashCoord_TestResultType FlashCoord_Test_RequestAccess_PFlash(void);
FlashCoord_TestResultType FlashCoord_Test_ReleaseAccess_Valid(void);
FlashCoord_TestResultType FlashCoord_Test_ReleaseAccess_Invalid(void);
FlashCoord_TestResultType FlashCoord_Test_Timeout_Handling(void);
FlashCoord_TestResultType FlashCoord_Test_Concurrent_Access(void);

/* AUTOSAR integration tests */
FlashCoord_TestResultType FlashCoord_Test_AutosarIntegration_Init(void);
FlashCoord_TestResultType FlashCoord_Test_AutosarIntegration_StatusCheck(void);
FlashCoord_TestResultType FlashCoord_Test_AutosarIntegration_WaitForIdle(void);

/* Error handling tests */
FlashCoord_TestResultType FlashCoord_Test_ErrorHandling_Init(void);
FlashCoord_TestResultType FlashCoord_Test_ErrorHandling_ReportError(void);
FlashCoord_TestResultType FlashCoord_Test_ErrorHandling_TimeoutMonitoring(void);
FlashCoord_TestResultType FlashCoord_Test_ErrorHandling_Recovery(void);

/* PFlash optimization tests */
FlashCoord_TestResultType FlashCoord_Test_PFlashOptimization_Init(void);
FlashCoord_TestResultType FlashCoord_Test_PFlashOptimization_ParallelAccess(void);
FlashCoord_TestResultType FlashCoord_Test_PFlashOptimization_ABSwap(void);

/* Logging tests */
FlashCoord_TestResultType FlashCoord_Test_Logging_Init(void);
FlashCoord_TestResultType FlashCoord_Test_Logging_LogMessage(void);
FlashCoord_TestResultType FlashCoord_Test_Logging_PerformanceMetrics(void);

/* OTA safe access tests */
FlashCoord_TestResultType FlashCoord_Test_OTA_SafeAccess_Init(void);
FlashCoord_TestResultType FlashCoord_Test_OTA_SafeAccess_ReadNvmData(void);
FlashCoord_TestResultType FlashCoord_Test_OTA_SafeAccess_WriteNvmData(void);
FlashCoord_TestResultType FlashCoord_Test_OTA_SafeAccess_EraseFlash(void);

/***************************************************************************/
/*--------------------Integration Test Function Declarations--------------*/
/***************************************************************************/

/* System integration tests */
FlashCoord_TestResultType FlashCoord_Test_Integration_FullSystem(void);
FlashCoord_TestResultType FlashCoord_Test_Integration_OTA_AUTOSAR_Coordination(void);
FlashCoord_TestResultType FlashCoord_Test_Integration_DFlash_Conflict_Resolution(void);
FlashCoord_TestResultType FlashCoord_Test_Integration_PFlash_Parallel_Access(void);
FlashCoord_TestResultType FlashCoord_Test_Integration_Error_Recovery_Scenarios(void);

/***************************************************************************/
/*----------------------Stress Test Function Declarations-----------------*/
/***************************************************************************/

/* Stress and performance tests */
FlashCoord_TestResultType FlashCoord_Test_Stress_HighFrequency_Access(void);
FlashCoord_TestResultType FlashCoord_Test_Stress_Long_Duration_Operations(void);
FlashCoord_TestResultType FlashCoord_Test_Stress_Multiple_Owners_Contention(void);
FlashCoord_TestResultType FlashCoord_Test_Stress_Memory_Leak_Detection(void);

/***************************************************************************/
/*--------------------------Test Utility Functions------------------------*/
/***************************************************************************/

/**
 * @brief Assert test condition
 * 
 * @details This macro asserts a test condition and reports failure if false.
 * 
 * @param[in] condition             Condition to test
 * @param[in] message               Failure message
 */
#define FLASH_COORD_TEST_ASSERT(condition, message) \
    do { \
        if (!(condition)) { \
            FlashCoord_Test_ReportFailure(__FILE__, __LINE__, message); \
            return FLASH_COORD_TEST_FAIL; \
        } \
    } while(0)

/**
 * @brief Report test failure
 * 
 * @details This function reports a test failure with location information.
 * 
 * @param[in] file                  Source file name
 * @param[in] line                  Source line number
 * @param[in] message               Failure message
 * @return None
 */
void FlashCoord_Test_ReportFailure(const char* file, uint32 line, const char* message);

#endif /* FLASH_COORDINATOR_TEST_H */
