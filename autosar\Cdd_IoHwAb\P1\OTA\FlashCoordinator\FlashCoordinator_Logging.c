/*****************************************************************************
*
*   All Contents@CopyRight Of Neusoft Corporation
*
*   All rights reserved.
*
*   Filename: FlashCoordinator_Logging.c   
*
*   Description: Logging and monitoring system implementation
*
*   Revision History:
*
*   Date            Author          Description
*   ----------      ----------      -----------------------------------------
*   2025-01-31      AI-Assistant    Initial created for logging and monitoring
*
*****************************************************************************/

/***************************************************************************/
/*---------------------------------Includes--------------------------------*/
/***************************************************************************/
#include "FlashCoordinator_Logging.h"
#include <string.h>

/***************************************************************************/
/*---------------------------------Macros----------------------------------*/
/***************************************************************************/

/* Module initialization pattern */
#define FLASH_COORD_LOGGING_INIT_PATTERN          0x12345678uL

/* String copy safety */
#define FLASH_COORD_SAFE_STRCPY(dest, src, size) \
    do { \
        strncpy(dest, src, size - 1); \
        dest[size - 1] = '\0'; \
    } while(0)

/***************************************************************************/
/*-----------------------------Type Definitions----------------------------*/
/***************************************************************************/

/** Logging control structure */
typedef struct
{
    uint32 initPattern;                     /**< Initialization pattern */
    boolean loggingEnabled;                 /**< Logging enable flag */
    FlashCoord_LogLevelType minLogLevel;    /**< Minimum log level */
    FlashCoord_LogEntryType logBuffer[FLASH_COORD_LOG_BUFFER_SIZE]; /**< Log buffer */
    uint32 logBufferIndex;                  /**< Current log buffer index */
    uint32 logBufferCount;                  /**< Number of entries in buffer */
    FlashCoord_RuntimeMonitorType monitor;  /**< Runtime monitor */
} FlashCoord_LoggingControlType;

/***************************************************************************/
/*------------------------------Static Data--------------------------------*/
/***************************************************************************/

/** Logging control structure */
static FlashCoord_LoggingControlType FlashCoord_LoggingControl = {0};

/** Log level strings */
static const char* FlashCoord_LogLevelStrings[] = {
    "DEBUG",
    "INFO",
    "WARNING",
    "ERROR",
    "CRITICAL"
};

/** Owner strings */
static const char* FlashCoord_OwnerStrings[] = {
    "NONE",
    "NVM",
    "FEE",
    "FLS",
    "OTA",
    "SYSTEM"
};

/***************************************************************************/
/*--------------------------Static Function Declarations------------------*/
/***************************************************************************/

static boolean FlashCoord_Logging_IsInitialized(void);
static uint32 FlashCoord_Logging_GetCurrentTime(void);
static void FlashCoord_Logging_AddLogEntry(const FlashCoord_LogEntryType* entry);
static void FlashCoord_Logging_UpdatePerformanceMetrics(
    uint32 accessTimeMs,
    FlashCoord_ResultType result
);
static const char* FlashCoord_Logging_GetLogLevelString(FlashCoord_LogLevelType level);
static const char* FlashCoord_Logging_GetOwnerString(FlashCoord_OwnerType owner);

/***************************************************************************/
/*--------------------External Function Implementations--------------------*/
/***************************************************************************/

/**
 * @brief Initialize logging system
 */
FlashCoord_ResultType FlashCoord_Logging_Init(void)
{
    uint32 i;
    
    /* Initialize control structure */
    FlashCoord_LoggingControl.initPattern = FLASH_COORD_LOGGING_INIT_PATTERN;
    FlashCoord_LoggingControl.loggingEnabled = TRUE;
    FlashCoord_LoggingControl.minLogLevel = FLASH_COORD_LOG_DEBUG;
    FlashCoord_LoggingControl.logBufferIndex = 0u;
    FlashCoord_LoggingControl.logBufferCount = 0u;
    
    /* Initialize log buffer */
    for (i = 0u; i < FLASH_COORD_LOG_BUFFER_SIZE; i++)
    {
        FlashCoord_LoggingControl.logBuffer[i].timestamp = 0u;
        FlashCoord_LoggingControl.logBuffer[i].level = FLASH_COORD_LOG_DEBUG;
        FlashCoord_LoggingControl.logBuffer[i].owner = FLASH_COORD_OWNER_NONE;
        FlashCoord_LoggingControl.logBuffer[i].address = 0u;
        FlashCoord_LoggingControl.logBuffer[i].errorCode = 0u;
        FlashCoord_LoggingControl.logBuffer[i].message[0] = '\0';
    }
    
    /* Initialize runtime monitor */
    FlashCoord_LoggingControl.monitor.monitoringActive = FALSE;
    FlashCoord_LoggingControl.monitor.lastMonitorTime = 0u;
    FlashCoord_LoggingControl.monitor.monitoringCycles = 0u;
    FlashCoord_LoggingControl.monitor.systemLoadPercent = 0u;
    FlashCoord_LoggingControl.monitor.memoryUsageBytes = 0u;
    
    /* Initialize performance metrics */
    FlashCoord_LoggingControl.monitor.metrics.totalOperations = 0u;
    FlashCoord_LoggingControl.monitor.metrics.averageAccessTimeMs = 0u;
    FlashCoord_LoggingControl.monitor.metrics.maxAccessTimeMs = 0u;
    FlashCoord_LoggingControl.monitor.metrics.minAccessTimeMs = 0xFFFFFFFFuL;
    FlashCoord_LoggingControl.monitor.metrics.timeoutCount = 0u;
    FlashCoord_LoggingControl.monitor.metrics.errorCount = 0u;
    FlashCoord_LoggingControl.monitor.metrics.warningCount = 0u;
    
    /* Log initialization */
    FlashCoord_Logging_LogMessage(
        FLASH_COORD_LOG_INFO,
        FLASH_COORD_OWNER_SYSTEM,
        0u,
        0u,
        "FlashCoordinator Logging System Initialized"
    );
    
    return FLASH_COORD_OK;
}

/**
 * @brief Log a message
 */
FlashCoord_ResultType FlashCoord_Logging_LogMessage(
    FlashCoord_LogLevelType level,
    FlashCoord_OwnerType owner,
    uint32 address,
    uint32 errorCode,
    const char* message
)
{
    FlashCoord_LogEntryType logEntry;
    
    if (!FlashCoord_Logging_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }
    
    if (message == NULL_PTR)
    {
        return FLASH_COORD_INVALID_PARAM;
    }
    
    /* Check log level filtering */
    if (level < FlashCoord_LoggingControl.minLogLevel)
    {
        return FLASH_COORD_OK; /* Filtered out */
    }
    
#if (FLASH_COORD_LOGGING_ENABLED == STD_ON)
    
    /* Create log entry */
    logEntry.timestamp = FlashCoord_Logging_GetCurrentTime();
    logEntry.level = level;
    logEntry.owner = owner;
    logEntry.address = address;
    logEntry.errorCode = errorCode;
    FLASH_COORD_SAFE_STRCPY(logEntry.message, message, FLASH_COORD_LOG_MESSAGE_MAX_LENGTH);
    
    /* Add to log buffer */
    FlashCoord_Logging_AddLogEntry(&logEntry);
    
    /* Update warning/error counters */
    if (level == FLASH_COORD_LOG_WARNING)
    {
        FlashCoord_LoggingControl.monitor.metrics.warningCount++;
    }
    else if (level >= FLASH_COORD_LOG_ERROR)
    {
        FlashCoord_LoggingControl.monitor.metrics.errorCount++;
    }
    
#endif /* FLASH_COORD_LOGGING_ENABLED */
    
    return FLASH_COORD_OK;
}

/**
 * @brief Log Flash access operation
 */
FlashCoord_ResultType FlashCoord_Logging_LogFlashAccess(
    FlashCoord_OwnerType owner,
    uint32 address,
    uint32 length,
    uint32 accessTimeMs,
    FlashCoord_ResultType result
)
{
    char logMessage[FLASH_COORD_LOG_MESSAGE_MAX_LENGTH];
    FlashCoord_LogLevelType logLevel;
    
    if (!FlashCoord_Logging_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }
    
    /* Update performance metrics */
    FlashCoord_Logging_UpdatePerformanceMetrics(accessTimeMs, result);
    
    /* Determine log level based on result and performance */
    if (result != FLASH_COORD_OK)
    {
        logLevel = FLASH_COORD_LOG_ERROR;
    }
    else if (accessTimeMs > FLASH_COORD_PERF_CRITICAL_THRESHOLD_MS)
    {
        logLevel = FLASH_COORD_LOG_CRITICAL;
    }
    else if (accessTimeMs > FLASH_COORD_PERF_WARNING_THRESHOLD_MS)
    {
        logLevel = FLASH_COORD_LOG_WARNING;
    }
    else
    {
        logLevel = FLASH_COORD_LOG_DEBUG;
    }
    
    /* Format log message */
    snprintf(logMessage, sizeof(logMessage),
             "Flash Access: %s addr=0x%08X len=%u time=%ums result=%u",
             FlashCoord_Logging_GetOwnerString(owner),
             address,
             length,
             accessTimeMs,
             (uint32)result);
    
    return FlashCoord_Logging_LogMessage(logLevel, owner, address, (uint32)result, logMessage);
}

/**
 * @brief Get log entries
 */
FlashCoord_ResultType FlashCoord_Logging_GetLogEntries(
    FlashCoord_LogEntryType* entries,
    uint32 maxEntries,
    uint32* actualEntries
)
{
    uint32 i;
    uint32 copyCount;
    uint32 startIndex;
    
    if (entries == NULL_PTR || actualEntries == NULL_PTR)
    {
        return FLASH_COORD_INVALID_PARAM;
    }
    
    if (!FlashCoord_Logging_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }
    
    /* Determine how many entries to copy */
    copyCount = (maxEntries < FlashCoord_LoggingControl.logBufferCount) ? 
                maxEntries : FlashCoord_LoggingControl.logBufferCount;
    
    /* Determine start index (get most recent entries) */
    if (FlashCoord_LoggingControl.logBufferCount < FLASH_COORD_LOG_BUFFER_SIZE)
    {
        startIndex = 0u;
    }
    else
    {
        startIndex = (FlashCoord_LoggingControl.logBufferIndex + FLASH_COORD_LOG_BUFFER_SIZE - copyCount) % 
                     FLASH_COORD_LOG_BUFFER_SIZE;
    }
    
    /* Copy entries */
    for (i = 0u; i < copyCount; i++)
    {
        uint32 bufferIndex = (startIndex + i) % FLASH_COORD_LOG_BUFFER_SIZE;
        entries[i] = FlashCoord_LoggingControl.logBuffer[bufferIndex];
    }
    
    *actualEntries = copyCount;
    
    return FLASH_COORD_OK;
}

/**
 * @brief Clear log buffer
 */
FlashCoord_ResultType FlashCoord_Logging_ClearLog(void)
{
    if (!FlashCoord_Logging_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }

    FlashCoord_LoggingControl.logBufferIndex = 0u;
    FlashCoord_LoggingControl.logBufferCount = 0u;

    return FLASH_COORD_OK;
}

/**
 * @brief Start runtime monitoring
 */
FlashCoord_ResultType FlashCoord_Logging_StartMonitoring(void)
{
    if (!FlashCoord_Logging_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }

    FlashCoord_LoggingControl.monitor.monitoringActive = TRUE;
    FlashCoord_LoggingControl.monitor.lastMonitorTime = FlashCoord_Logging_GetCurrentTime();
    FlashCoord_LoggingControl.monitor.monitoringCycles = 0u;

    FlashCoord_Logging_LogMessage(
        FLASH_COORD_LOG_INFO,
        FLASH_COORD_OWNER_SYSTEM,
        0u,
        0u,
        "Runtime monitoring started"
    );

    return FLASH_COORD_OK;
}

/**
 * @brief Stop runtime monitoring
 */
FlashCoord_ResultType FlashCoord_Logging_StopMonitoring(void)
{
    if (!FlashCoord_Logging_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }

    FlashCoord_LoggingControl.monitor.monitoringActive = FALSE;

    FlashCoord_Logging_LogMessage(
        FLASH_COORD_LOG_INFO,
        FLASH_COORD_OWNER_SYSTEM,
        0u,
        0u,
        "Runtime monitoring stopped"
    );

    return FLASH_COORD_OK;
}

/**
 * @brief Get performance metrics
 */
FlashCoord_ResultType FlashCoord_Logging_GetPerformanceMetrics(
    FlashCoord_PerformanceMetricsType* metrics
)
{
    if (metrics == NULL_PTR)
    {
        return FLASH_COORD_INVALID_PARAM;
    }

    if (!FlashCoord_Logging_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }

    *metrics = FlashCoord_LoggingControl.monitor.metrics;

    return FLASH_COORD_OK;
}

/**
 * @brief Reset performance metrics
 */
FlashCoord_ResultType FlashCoord_Logging_ResetPerformanceMetrics(void)
{
    if (!FlashCoord_Logging_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }

    /* Reset all metrics */
    FlashCoord_LoggingControl.monitor.metrics.totalOperations = 0u;
    FlashCoord_LoggingControl.monitor.metrics.averageAccessTimeMs = 0u;
    FlashCoord_LoggingControl.monitor.metrics.maxAccessTimeMs = 0u;
    FlashCoord_LoggingControl.monitor.metrics.minAccessTimeMs = 0xFFFFFFFFuL;
    FlashCoord_LoggingControl.monitor.metrics.timeoutCount = 0u;
    FlashCoord_LoggingControl.monitor.metrics.errorCount = 0u;
    FlashCoord_LoggingControl.monitor.metrics.warningCount = 0u;

    FlashCoord_Logging_LogMessage(
        FLASH_COORD_LOG_INFO,
        FLASH_COORD_OWNER_SYSTEM,
        0u,
        0u,
        "Performance metrics reset"
    );

    return FLASH_COORD_OK;
}

/**
 * @brief Set log level
 */
FlashCoord_ResultType FlashCoord_Logging_SetLogLevel(
    FlashCoord_LogLevelType level
)
{
    if (level > FLASH_COORD_LOG_CRITICAL)
    {
        return FLASH_COORD_INVALID_PARAM;
    }

    if (!FlashCoord_Logging_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }

    FlashCoord_LoggingControl.minLogLevel = level;

    return FLASH_COORD_OK;
}

/**
 * @brief Main function for logging and monitoring
 */
void FlashCoord_Logging_MainFunction(void)
{
    uint32 currentTime;
    uint32 elapsedTime;

    if (!FlashCoord_Logging_IsInitialized())
    {
        return;
    }

    currentTime = FlashCoord_Logging_GetCurrentTime();

    /* Runtime monitoring tasks */
    if (FlashCoord_LoggingControl.monitor.monitoringActive)
    {
        elapsedTime = currentTime - FlashCoord_LoggingControl.monitor.lastMonitorTime;

        if (elapsedTime >= FLASH_COORD_MONITOR_INTERVAL_MS)
        {
            FlashCoord_LoggingControl.monitor.monitoringCycles++;
            FlashCoord_LoggingControl.monitor.lastMonitorTime = currentTime;

            /* Update system metrics */
            /* Note: These would be implemented based on available system services */
            FlashCoord_LoggingControl.monitor.systemLoadPercent = 50u; /* Placeholder */
            FlashCoord_LoggingControl.monitor.memoryUsageBytes = 1024u; /* Placeholder */

            /* Periodic statistics logging */
            if ((FlashCoord_LoggingControl.monitor.monitoringCycles %
                 (FLASH_COORD_STATISTICS_UPDATE_INTERVAL_MS / FLASH_COORD_MONITOR_INTERVAL_MS)) == 0u)
            {
                char statsMessage[FLASH_COORD_LOG_MESSAGE_MAX_LENGTH];
                snprintf(statsMessage, sizeof(statsMessage),
                         "Stats: ops=%u avg=%ums max=%ums errors=%u warnings=%u",
                         FlashCoord_LoggingControl.monitor.metrics.totalOperations,
                         FlashCoord_LoggingControl.monitor.metrics.averageAccessTimeMs,
                         FlashCoord_LoggingControl.monitor.metrics.maxAccessTimeMs,
                         FlashCoord_LoggingControl.monitor.metrics.errorCount,
                         FlashCoord_LoggingControl.monitor.metrics.warningCount);

                FlashCoord_Logging_LogMessage(
                    FLASH_COORD_LOG_INFO,
                    FLASH_COORD_OWNER_SYSTEM,
                    0u,
                    0u,
                    statsMessage
                );
            }
        }
    }
}

/***************************************************************************/
/*--------------------------Static Function Implementations---------------*/
/***************************************************************************/

/**
 * @brief Check if logging is initialized
 */
static boolean FlashCoord_Logging_IsInitialized(void)
{
    return (FlashCoord_LoggingControl.initPattern == FLASH_COORD_LOGGING_INIT_PATTERN);
}

/**
 * @brief Get current system time
 */
static uint32 FlashCoord_Logging_GetCurrentTime(void)
{
    /* Implementation depends on available timer service */
    /* For now, use a simple counter - should be replaced with actual timer */
    static uint32 timeCounter = 0u;
    return ++timeCounter;
}

/**
 * @brief Add log entry to buffer
 */
static void FlashCoord_Logging_AddLogEntry(const FlashCoord_LogEntryType* entry)
{
    if (entry == NULL_PTR)
    {
        return;
    }

    /* Add entry to circular buffer */
    FlashCoord_LoggingControl.logBuffer[FlashCoord_LoggingControl.logBufferIndex] = *entry;

    /* Update buffer index */
    FlashCoord_LoggingControl.logBufferIndex =
        (FlashCoord_LoggingControl.logBufferIndex + 1u) % FLASH_COORD_LOG_BUFFER_SIZE;

    /* Update buffer count */
    if (FlashCoord_LoggingControl.logBufferCount < FLASH_COORD_LOG_BUFFER_SIZE)
    {
        FlashCoord_LoggingControl.logBufferCount++;
    }
}

/**
 * @brief Update performance metrics
 */
static void FlashCoord_Logging_UpdatePerformanceMetrics(
    uint32 accessTimeMs,
    FlashCoord_ResultType result
)
{
    FlashCoord_PerformanceMetricsType* metrics = &FlashCoord_LoggingControl.monitor.metrics;

    metrics->totalOperations++;

    /* Update access time statistics */
    if (accessTimeMs > metrics->maxAccessTimeMs)
    {
        metrics->maxAccessTimeMs = accessTimeMs;
    }

    if (accessTimeMs < metrics->minAccessTimeMs)
    {
        metrics->minAccessTimeMs = accessTimeMs;
    }

    /* Calculate average access time */
    if (metrics->totalOperations > 0u)
    {
        metrics->averageAccessTimeMs =
            ((metrics->averageAccessTimeMs * (metrics->totalOperations - 1u)) + accessTimeMs) /
            metrics->totalOperations;
    }

    /* Update result-based counters */
    if (result == FLASH_COORD_TIMEOUT)
    {
        metrics->timeoutCount++;
    }
    else if (result != FLASH_COORD_OK)
    {
        metrics->errorCount++;
    }
}

/**
 * @brief Get log level string
 */
static const char* FlashCoord_Logging_GetLogLevelString(FlashCoord_LogLevelType level)
{
    if (level < (sizeof(FlashCoord_LogLevelStrings) / sizeof(FlashCoord_LogLevelStrings[0])))
    {
        return FlashCoord_LogLevelStrings[level];
    }

    return "UNKNOWN";
}

/**
 * @brief Get owner string
 */
static const char* FlashCoord_Logging_GetOwnerString(FlashCoord_OwnerType owner)
{
    if (owner < (sizeof(FlashCoord_OwnerStrings) / sizeof(FlashCoord_OwnerStrings[0])))
    {
        return FlashCoord_OwnerStrings[owner];
    }

    return "UNKNOWN";
}
