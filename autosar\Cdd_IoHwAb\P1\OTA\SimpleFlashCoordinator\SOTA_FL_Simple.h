/*****************************************************************************
*
*   All Contents@CopyRight Of Neusoft Corporation
*
*   All rights reserved.
*
*   Filename: SOTA_FL_Simple.h   
*
*   Description: Simplified OTA Flash operations with DFlash coordination
*                Drop-in replacement for original OTA Flash functions
*                with minimal coordination for DFlash conflict resolution
*
*   Revision History:
*
*   Date            Author          Description
*   ----------      ----------      -----------------------------------------
*   2025-01-31      AI-Assistant    Initial created for simplified OTA coordination
*
*****************************************************************************/

#ifndef SOTA_FL_SIMPLE_H
#define SOTA_FL_SIMPLE_H

/***************************************************************************/
/*---------------------------------Includes--------------------------------*/
/***************************************************************************/
#include "Std_Types.h"
#include "SOTA_FL.h"
#include "SimpleFlashCoordinator.h"

/***************************************************************************/
/*----------------------------------Macros---------------------------------*/
/***************************************************************************/

/* Enable simple coordination */
#define SOTA_FL_SIMPLE_COORDINATION_ENABLED      STD_ON

/* Default timeout for DFlash operations */
#define SOTA_FL_SIMPLE_DEFAULT_TIMEOUT_MS        5000u

/***************************************************************************/
/*--------------------External Function Declarations-----------------------*/
/***************************************************************************/

/**
 * @brief Initialize simple OTA Flash coordination
 * 
 * @details Initializes the simple coordination system for OTA Flash operations
 * 
 * @param[in] None
 * @return Sota_FL_ResultType
 * @retval SOTA_FL_OK           Initialization successful
 * @retval SOTA_FL_FAILED       Initialization failed
 */
Sota_FL_ResultType SOTA_FL_Simple_Init(void);

/**
 * @brief Safe NVM data read with coordination
 * 
 * @details Drop-in replacement for Sota_FL_ReadNvmData with DFlash coordination
 * 
 * @param[in] addr              Address to read from
 * @return uint32               Read data value
 */
uint32 SOTA_FL_Simple_ReadNvmData(uint32 addr);

/**
 * @brief Safe data write status erase with coordination
 * 
 * @details Drop-in replacement for Sota_FL_EraseDataWrStatus with DFlash coordination
 * 
 * @param[in] None
 * @return Sota_FL_ResultType   Operation result
 */
Sota_FL_ResultType SOTA_FL_Simple_EraseDataWrStatus(void);

/**
 * @brief Safe ECU reset flag write with coordination
 * 
 * @details Drop-in replacement for Sota_FL_WriteEcuResetFlag with DFlash coordination
 * 
 * @param[in] None
 * @return Sota_FL_ResultType   Operation result
 */
Sota_FL_ResultType SOTA_FL_Simple_WriteEcuResetFlag(void);

/**
 * @brief Safe data write status update with coordination
 * 
 * @details Safe version of data write status update with DFlash coordination
 * 
 * @param[in] None
 * @return Sota_FL_ResultType   Operation result
 */
Sota_FL_ResultType SOTA_FL_Simple_DataWriteStatusUpdate(void);

/**
 * @brief Safe block not erased flag write with coordination
 * 
 * @details Safe version of block not erased flag write with DFlash coordination
 * 
 * @param[in] None
 * @return Sota_FL_ResultType   Operation result
 */
Sota_FL_ResultType SOTA_FL_Simple_WriteBlockNotErasedFlag(void);

/**
 * @brief Safe CALID write to NVM with coordination
 * 
 * @details Safe version of CALID write with DFlash coordination
 * 
 * @param[in] calid             CALID value to write
 * @return Sota_FL_ResultType   Operation result
 */
Sota_FL_ResultType SOTA_FL_Simple_WriteCalidToNvm(uint32 calid);

/**
 * @brief Safe breakpoint information write with coordination
 * 
 * @details Safe version of breakpoint info write with DFlash coordination
 * 
 * @param[in] None
 * @return Sota_FL_ResultType   Operation result
 */
Sota_FL_ResultType SOTA_FL_Simple_WriteBreakpointInfo(void);

/* Convenience macros for easy migration */
#if (SOTA_FL_SIMPLE_COORDINATION_ENABLED == STD_ON)

/* Replace original functions with coordinated versions */
#define Sota_FL_ReadNvmData(addr)                 SOTA_FL_Simple_ReadNvmData(addr)
#define Sota_FL_EraseDataWrStatus()               SOTA_FL_Simple_EraseDataWrStatus()
#define Sota_FL_WriteEcuResetFlag()               SOTA_FL_Simple_WriteEcuResetFlag()

/* Additional safe functions */
#define SOTA_FL_DataWriteStatusUpdate()           SOTA_FL_Simple_DataWriteStatusUpdate()
#define SOTA_FL_WriteBlockNotErasedFlag()         SOTA_FL_Simple_WriteBlockNotErasedFlag()
#define SOTA_FL_WriteCalidToNvm(calid)            SOTA_FL_Simple_WriteCalidToNvm(calid)
#define SOTA_FL_WriteBreakpointInfo()             SOTA_FL_Simple_WriteBreakpointInfo()

#endif /* SOTA_FL_SIMPLE_COORDINATION_ENABLED */

#endif /* SOTA_FL_SIMPLE_H */
