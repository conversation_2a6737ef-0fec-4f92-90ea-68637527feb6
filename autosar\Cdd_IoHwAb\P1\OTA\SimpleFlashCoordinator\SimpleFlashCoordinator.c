/*****************************************************************************
*
*   All Contents@CopyRight Of Neusoft Corporation
*
*   All rights reserved.
*
*   Filename: SimpleFlashCoordinator.c   
*
*   Description: Simplified Flash Coordinator implementation
*
*   Revision History:
*
*   Date            Author          Description
*   ----------      ----------      -----------------------------------------
*   2025-01-31      AI-Assistant    Initial created for simplified coordination
*
*****************************************************************************/

/***************************************************************************/
/*---------------------------------Includes--------------------------------*/
/***************************************************************************/
#include "SimpleFlashCoordinator.h"
#include "Os.h"

/***************************************************************************/
/*---------------------------------Macros----------------------------------*/
/***************************************************************************/

/* Initialization pattern */
#define SIMPLE_FLASH_COORD_INIT_PATTERN           0x12345678uL

/* Retry delay in milliseconds */
#define SIMPLE_FLASH_COORD_RETRY_DELAY_MS         10u

/***************************************************************************/
/*-----------------------------Type Definitions----------------------------*/
/***************************************************************************/

/** Simple Flash Coordinator control structure */
typedef struct
{
    uint32 initPattern;                     /**< Initialization pattern */
    SimpleFlashCoord_OwnerType currentOwner; /**< Current DFlash owner */
    uint32 accessStartTime;                 /**< Access start time */
    boolean accessActive;                   /**< Access active flag */
} SimpleFlashCoord_ControlType;

/***************************************************************************/
/*------------------------------Static Data--------------------------------*/
/***************************************************************************/

/** Flash Coordinator control structure */
static SimpleFlashCoord_ControlType SimpleFlashCoord_Control = {0};

/***************************************************************************/
/*--------------------------Static Function Declarations------------------*/
/***************************************************************************/

static boolean SimpleFlashCoord_IsInitialized(void);
static boolean SimpleFlashCoord_AreAutosarModulesIdle(void);
static uint32 SimpleFlashCoord_GetCurrentTime(void);
static void SimpleFlashCoord_Delay(uint32 delayMs);

/***************************************************************************/
/*--------------------External Function Implementations--------------------*/
/***************************************************************************/

/**
 * @brief Initialize Simple Flash Coordinator
 */
SimpleFlashCoord_ResultType SimpleFlashCoord_Init(void)
{
    /* Initialize control structure */
    SimpleFlashCoord_Control.initPattern = SIMPLE_FLASH_COORD_INIT_PATTERN;
    SimpleFlashCoord_Control.currentOwner = SIMPLE_FLASH_COORD_OWNER_NONE;
    SimpleFlashCoord_Control.accessStartTime = 0u;
    SimpleFlashCoord_Control.accessActive = FALSE;
    
    return SIMPLE_FLASH_COORD_OK;
}

/**
 * @brief Request DFlash access
 */
SimpleFlashCoord_ResultType SimpleFlashCoord_RequestDFlashAccess(
    SimpleFlashCoord_OwnerType owner,
    uint32 address,
    uint32 timeoutMs
)
{
    uint32 startTime;
    uint32 elapsedTime = 0u;
    uint32 retryCount = 0u;
    
    /* Check initialization */
    if (!SimpleFlashCoord_IsInitialized())
    {
        return SIMPLE_FLASH_COORD_ERROR;
    }
    
    /* Check if address requires coordination */
    if (!SimpleFlashCoord_RequiresCoordination(address))
    {
        /* PFlash access - no coordination needed */
        return SIMPLE_FLASH_COORD_OK;
    }
    
    startTime = SimpleFlashCoord_GetCurrentTime();
    
    /* Retry loop with timeout */
    while (elapsedTime < timeoutMs && retryCount < SIMPLE_FLASH_COORD_MAX_RETRIES)
    {
        /* Enter critical section */
        SuspendAllInterrupts();
        
        /* Check if DFlash is available */
        if (!SimpleFlashCoord_Control.accessActive)
        {
            /* Check AUTOSAR modules status */
            if (SimpleFlashCoord_AreAutosarModulesIdle())
            {
                /* Grant access */
                SimpleFlashCoord_Control.currentOwner = owner;
                SimpleFlashCoord_Control.accessStartTime = SimpleFlashCoord_GetCurrentTime();
                SimpleFlashCoord_Control.accessActive = TRUE;
                
                /* Exit critical section */
                ResumeAllInterrupts();
                
                return SIMPLE_FLASH_COORD_OK;
            }
        }
        
        /* Exit critical section */
        ResumeAllInterrupts();
        
        /* Wait and retry */
        SimpleFlashCoord_Delay(SIMPLE_FLASH_COORD_RETRY_DELAY_MS);
        
        elapsedTime = SimpleFlashCoord_GetCurrentTime() - startTime;
        retryCount++;
    }
    
    /* Timeout or max retries reached */
    return (elapsedTime >= timeoutMs) ? SIMPLE_FLASH_COORD_TIMEOUT : SIMPLE_FLASH_COORD_BUSY;
}

/**
 * @brief Release DFlash access
 */
SimpleFlashCoord_ResultType SimpleFlashCoord_ReleaseDFlashAccess(
    SimpleFlashCoord_OwnerType owner
)
{
    SimpleFlashCoord_ResultType result = SIMPLE_FLASH_COORD_ERROR;
    
    /* Check initialization */
    if (!SimpleFlashCoord_IsInitialized())
    {
        return SIMPLE_FLASH_COORD_ERROR;
    }
    
    /* Enter critical section */
    SuspendAllInterrupts();
    
    /* Check if the owner matches */
    if (SimpleFlashCoord_Control.currentOwner == owner && SimpleFlashCoord_Control.accessActive)
    {
        /* Release access */
        SimpleFlashCoord_Control.currentOwner = SIMPLE_FLASH_COORD_OWNER_NONE;
        SimpleFlashCoord_Control.accessStartTime = 0u;
        SimpleFlashCoord_Control.accessActive = FALSE;
        result = SIMPLE_FLASH_COORD_OK;
    }
    
    /* Exit critical section */
    ResumeAllInterrupts();
    
    return result;
}

/**
 * @brief Check if DFlash is accessible
 */
boolean SimpleFlashCoord_IsDFlashAccessible(SimpleFlashCoord_OwnerType owner)
{
    boolean accessible = FALSE;
    
    if (!SimpleFlashCoord_IsInitialized())
    {
        return FALSE;
    }
    
    /* Enter critical section */
    SuspendAllInterrupts();
    
    if (!SimpleFlashCoord_Control.accessActive)
    {
        accessible = SimpleFlashCoord_AreAutosarModulesIdle();
    }
    else if (SimpleFlashCoord_Control.currentOwner == owner)
    {
        accessible = TRUE;
    }
    
    /* Exit critical section */
    ResumeAllInterrupts();
    
    return accessible;
}

/**
 * @brief Check if address requires coordination
 */
boolean SimpleFlashCoord_RequiresCoordination(uint32 address)
{
    /* Check if address is in DFlash range */
    return ((address >= SIMPLE_FLASH_COORD_DFLASH0_START && 
             address <= SIMPLE_FLASH_COORD_DFLASH0_END) ||
            (address >= SIMPLE_FLASH_COORD_DFLASH1_START && 
             address <= SIMPLE_FLASH_COORD_DFLASH1_END));
}

/**
 * @brief Get current DFlash owner
 */
SimpleFlashCoord_OwnerType SimpleFlashCoord_GetCurrentOwner(void)
{
    SimpleFlashCoord_OwnerType owner = SIMPLE_FLASH_COORD_OWNER_NONE;
    
    if (SimpleFlashCoord_IsInitialized())
    {
        /* Enter critical section */
        SuspendAllInterrupts();
        owner = SimpleFlashCoord_Control.currentOwner;
        /* Exit critical section */
        ResumeAllInterrupts();
    }
    
    return owner;
}

/**
 * @brief Force release DFlash access
 */
SimpleFlashCoord_ResultType SimpleFlashCoord_ForceRelease(void)
{
    if (!SimpleFlashCoord_IsInitialized())
    {
        return SIMPLE_FLASH_COORD_ERROR;
    }
    
    /* Enter critical section */
    SuspendAllInterrupts();
    
    /* Force release */
    SimpleFlashCoord_Control.currentOwner = SIMPLE_FLASH_COORD_OWNER_NONE;
    SimpleFlashCoord_Control.accessStartTime = 0u;
    SimpleFlashCoord_Control.accessActive = FALSE;
    
    /* Exit critical section */
    ResumeAllInterrupts();
    
    return SIMPLE_FLASH_COORD_OK;
}

/***************************************************************************/
/*--------------------------Static Function Implementations---------------*/
/***************************************************************************/

/**
 * @brief Check if Simple Flash Coordinator is initialized
 */
static boolean SimpleFlashCoord_IsInitialized(void)
{
    return (SimpleFlashCoord_Control.initPattern == SIMPLE_FLASH_COORD_INIT_PATTERN);
}

/**
 * @brief Check if all AUTOSAR modules are idle
 */
static boolean SimpleFlashCoord_AreAutosarModulesIdle(void)
{
    boolean allIdle = TRUE;
    
    /* Check NVM status */
    NvM_Rb_StatusType nvmStatus;
    if (NvM_Rb_GetStatus(&nvmStatus) == E_OK)
    {
        if (nvmStatus != NVM_RB_STATUS_IDLE)
        {
            allIdle = FALSE;
        }
    }
    else
    {
        allIdle = FALSE;
    }
    
    /* Check FEE status */
    MemIf_StatusType feeStatus = Fee_GetStatus();
    if (feeStatus != MEMIF_IDLE)
    {
        allIdle = FALSE;
    }
    
    /* Check FLS status */
    MemIf_StatusType flsStatus = Fls_17_Dmu_GetStatus();
    if (flsStatus != MEMIF_IDLE)
    {
        allIdle = FALSE;
    }
    
    return allIdle;
}

/**
 * @brief Get current system time
 */
static uint32 SimpleFlashCoord_GetCurrentTime(void)
{
    /* Simple implementation - should be replaced with actual timer */
    static uint32 timeCounter = 0u;
    return ++timeCounter;
}

/**
 * @brief Simple delay function
 */
static void SimpleFlashCoord_Delay(uint32 delayMs)
{
    /* Simple busy wait - should be replaced with OS delay */
    volatile uint32 counter;
    uint32 loops = delayMs * 1000u;
    
    for (counter = 0u; counter < loops; counter++)
    {
        /* Busy wait */
    }
}
