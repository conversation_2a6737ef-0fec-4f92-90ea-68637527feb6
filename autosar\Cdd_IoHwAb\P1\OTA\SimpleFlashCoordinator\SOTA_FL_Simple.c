/*****************************************************************************
*
*   All Contents@CopyRight Of Neusoft Corporation
*
*   All rights reserved.
*
*   Filename: SOTA_FL_Simple.c   
*
*   Description: Simplified OTA Flash operations implementation
*
*   Revision History:
*
*   Date            Author          Description
*   ----------      ----------      -----------------------------------------
*   2025-01-31      AI-Assistant    Initial created for simplified OTA coordination
*
*****************************************************************************/

/***************************************************************************/
/*---------------------------------Includes--------------------------------*/
/***************************************************************************/
#include "SOTA_FL_Simple.h"
#include "FlsLoaderDriver.h"

/***************************************************************************/
/*---------------------------------Macros----------------------------------*/
/***************************************************************************/

/* Initialization pattern */
#define SOTA_FL_SIMPLE_INIT_PATTERN               0xABCD1234uL

/***************************************************************************/
/*-----------------------------Type Definitions----------------------------*/
/***************************************************************************/

/** Simple OTA Flash control structure */
typedef struct
{
    uint32 initPattern;                     /**< Initialization pattern */
    boolean coordinationEnabled;            /**< Coordination enable flag */
} SOTA_FL_SimpleControlType;

/***************************************************************************/
/*------------------------------Static Data--------------------------------*/
/***************************************************************************/

/** Simple OTA Flash control structure */
static SOTA_FL_SimpleControlType SOTA_FL_SimpleControl = {0};

/***************************************************************************/
/*--------------------------Static Function Declarations------------------*/
/***************************************************************************/

static boolean SOTA_FL_Simple_IsInitialized(void);
static Sota_FL_ResultType SOTA_FL_Simple_SafeFlashOperation(
    uint32 address,
    void (*operation)(void),
    Sota_FL_ResultType (*operationWithResult)(void)
);

/***************************************************************************/
/*--------------------External Function Implementations--------------------*/
/***************************************************************************/

/**
 * @brief Initialize simple OTA Flash coordination
 */
Sota_FL_ResultType SOTA_FL_Simple_Init(void)
{
    SimpleFlashCoord_ResultType coordResult;
    
    /* Initialize Simple Flash Coordinator */
    coordResult = SimpleFlashCoord_Init();
    if (coordResult != SIMPLE_FLASH_COORD_OK)
    {
        return SOTA_FL_FAILED;
    }
    
    /* Initialize control structure */
    SOTA_FL_SimpleControl.initPattern = SOTA_FL_SIMPLE_INIT_PATTERN;
    SOTA_FL_SimpleControl.coordinationEnabled = TRUE;
    
    return SOTA_FL_OK;
}

/**
 * @brief Safe NVM data read with coordination
 */
uint32 SOTA_FL_Simple_ReadNvmData(uint32 addr)
{
    uint32 data = 0xFFFFFFFFuL; /* Default value for read failure */
    SimpleFlashCoord_ResultType coordResult;
    
    if (!SOTA_FL_Simple_IsInitialized())
    {
        return data;
    }
    
    /* Check if coordination is required */
    if (SimpleFlashCoord_RequiresCoordination(addr))
    {
        /* Request DFlash access */
        coordResult = SimpleFlashCoord_RequestDFlashAccess(
            SIMPLE_FLASH_COORD_OWNER_OTA,
            addr,
            SOTA_FL_SIMPLE_DEFAULT_TIMEOUT_MS
        );
        
        if (coordResult == SIMPLE_FLASH_COORD_OK)
        {
            /* Perform safe read */
            data = (*(volatile uint32*)addr) & 0xFFFFFFFFuL;
            
            /* Release access */
            SimpleFlashCoord_ReleaseDFlashAccess(SIMPLE_FLASH_COORD_OWNER_OTA);
        }
    }
    else
    {
        /* PFlash access - no coordination needed */
        data = (*(volatile uint32*)addr) & 0xFFFFFFFFuL;
    }
    
    return data;
}

/**
 * @brief Safe data write status erase with coordination
 */
Sota_FL_ResultType SOTA_FL_Simple_EraseDataWrStatus(void)
{
    Sota_FL_ResultType result = SOTA_FL_FAILED;
    SimpleFlashCoord_ResultType coordResult;
    FlsLoader_ReturnType flsResult;
    
    if (!SOTA_FL_Simple_IsInitialized())
    {
        return SOTA_FL_FAILED;
    }
    
    /* Request DFlash access */
    coordResult = SimpleFlashCoord_RequestDFlashAccess(
        SIMPLE_FLASH_COORD_OWNER_OTA,
        SOTA_FL_NVM_ADDR_DATA_WR_STATUS,
        SOTA_FL_SIMPLE_DEFAULT_TIMEOUT_MS
    );
    
    if (coordResult == SIMPLE_FLASH_COORD_OK)
    {
        /* Perform Flash erase */
        flsResult = FlsLoader_Erase(SOTA_FL_NVM_ADDR_DATA_WR_STATUS, SOTA_FL_NVM_ONE_PAGES);
        result = (flsResult == SOTA_FLSLOADER_E_OK) ? SOTA_FL_OK : SOTA_FL_FAILED;
        
        /* Release access */
        SimpleFlashCoord_ReleaseDFlashAccess(SIMPLE_FLASH_COORD_OWNER_OTA);
    }
    
    return result;
}

/**
 * @brief Safe ECU reset flag write with coordination
 */
Sota_FL_ResultType SOTA_FL_Simple_WriteEcuResetFlag(void)
{
    Sota_FL_ResultType result = SOTA_FL_FAILED;
    SimpleFlashCoord_ResultType coordResult;
    FlsLoader_ReturnType flsResult;
    uint32 databuff[4] = {SOTA_FL_NVM_ECU_RESET_STATUS, SOTA_FL_NVM_ECU_RESET_STATUS, 
                          SOTA_FL_NVM_ECU_RESET_STATUS, SOTA_FL_NVM_ECU_RESET_STATUS};
    
    if (!SOTA_FL_Simple_IsInitialized())
    {
        return SOTA_FL_FAILED;
    }
    
    /* Request DFlash access */
    coordResult = SimpleFlashCoord_RequestDFlashAccess(
        SIMPLE_FLASH_COORD_OWNER_OTA,
        SOTA_FL_NVM_ADDR_RESET_STATUS,
        SOTA_FL_SIMPLE_DEFAULT_TIMEOUT_MS
    );
    
    if (coordResult == SIMPLE_FLASH_COORD_OK)
    {
        /* Perform Flash write */
        flsResult = FlsLoader_Write(SOTA_FL_NVM_ADDR_RESET_STATUS, SOTA_FL_NVM_ONE_PAGES, (const uint8*)databuff);
        result = (flsResult == SOTA_FLSLOADER_E_OK) ? SOTA_FL_OK : SOTA_FL_FAILED;
        
        /* Release access */
        SimpleFlashCoord_ReleaseDFlashAccess(SIMPLE_FLASH_COORD_OWNER_OTA);
    }
    
    return result;
}

/**
 * @brief Safe data write status update with coordination
 */
Sota_FL_ResultType SOTA_FL_Simple_DataWriteStatusUpdate(void)
{
    Sota_FL_ResultType result = SOTA_FL_FAILED;
    SimpleFlashCoord_ResultType coordResult;
    FlsLoader_ReturnType flsResult;
    uint32 databuff[4] = {SOTA_FL_NVM_DATA_WRITE_STATUS, SOTA_FL_NVM_DATA_WRITE_STATUS, 
                          SOTA_FL_NVM_DATA_WRITE_STATUS, SOTA_FL_NVM_DATA_WRITE_STATUS};
    
    if (!SOTA_FL_Simple_IsInitialized())
    {
        return SOTA_FL_FAILED;
    }
    
    /* Request DFlash access */
    coordResult = SimpleFlashCoord_RequestDFlashAccess(
        SIMPLE_FLASH_COORD_OWNER_OTA,
        SOTA_FL_NVM_ADDR_DATA_WR_STATUS,
        SOTA_FL_SIMPLE_DEFAULT_TIMEOUT_MS
    );
    
    if (coordResult == SIMPLE_FLASH_COORD_OK)
    {
        /* Perform Flash write */
        flsResult = FlsLoader_Write(SOTA_FL_NVM_ADDR_DATA_WR_STATUS, SOTA_FL_NVM_ONE_PAGES, (const uint8*)databuff);
        result = (flsResult == SOTA_FLSLOADER_E_OK) ? SOTA_FL_OK : SOTA_FL_FAILED;
        
        /* Release access */
        SimpleFlashCoord_ReleaseDFlashAccess(SIMPLE_FLASH_COORD_OWNER_OTA);
    }
    
    return result;
}

/**
 * @brief Safe block not erased flag write with coordination
 */
Sota_FL_ResultType SOTA_FL_Simple_WriteBlockNotErasedFlag(void)
{
    Sota_FL_ResultType result = SOTA_FL_FAILED;
    SimpleFlashCoord_ResultType coordResult;
    FlsLoader_ReturnType flsResult;
    uint32 databuff[4] = {SOTA_FL_BLOCK_NOT_ERASED_STATUS, SOTA_FL_BLOCK_NOT_ERASED_STATUS, 
                          SOTA_FL_BLOCK_NOT_ERASED_STATUS, SOTA_FL_BLOCK_NOT_ERASED_STATUS};
    
    if (!SOTA_FL_Simple_IsInitialized())
    {
        return SOTA_FL_FAILED;
    }
    
    /* Request DFlash access */
    coordResult = SimpleFlashCoord_RequestDFlashAccess(
        SIMPLE_FLASH_COORD_OWNER_OTA,
        SOTA_FL_BLOCK_ADDR_ERASED_STAUS,
        SOTA_FL_SIMPLE_DEFAULT_TIMEOUT_MS
    );
    
    if (coordResult == SIMPLE_FLASH_COORD_OK)
    {
        /* Perform Flash write */
        flsResult = FlsLoader_Write(SOTA_FL_BLOCK_ADDR_ERASED_STAUS, SOTA_FL_NVM_ONE_PAGES, (const uint8*)databuff);
        result = (flsResult == SOTA_FLSLOADER_E_OK) ? SOTA_FL_OK : SOTA_FL_FAILED;
        
        /* Release access */
        SimpleFlashCoord_ReleaseDFlashAccess(SIMPLE_FLASH_COORD_OWNER_OTA);
    }
    
    return result;
}

/**
 * @brief Safe CALID write to NVM with coordination
 */
Sota_FL_ResultType SOTA_FL_Simple_WriteCalidToNvm(uint32 calid)
{
    Sota_FL_ResultType result = SOTA_FL_FAILED;
    SimpleFlashCoord_ResultType coordResult;
    FlsLoader_ReturnType flsResult;
    uint32 databuff[4] = {calid, calid, calid, calid};
    
    if (!SOTA_FL_Simple_IsInitialized())
    {
        return SOTA_FL_FAILED;
    }
    
    /* Request DFlash access */
    coordResult = SimpleFlashCoord_RequestDFlashAccess(
        SIMPLE_FLASH_COORD_OWNER_OTA,
        SOTA_FL_NVM_ADDR_CAILID,
        SOTA_FL_SIMPLE_DEFAULT_TIMEOUT_MS
    );
    
    if (coordResult == SIMPLE_FLASH_COORD_OK)
    {
        /* Perform Flash write */
        flsResult = FlsLoader_Write(SOTA_FL_NVM_ADDR_CAILID, SOTA_FL_NVM_ONE_PAGES, (const uint8*)databuff);
        result = (flsResult == SOTA_FLSLOADER_E_OK) ? SOTA_FL_OK : SOTA_FL_FAILED;
        
        /* Release access */
        SimpleFlashCoord_ReleaseDFlashAccess(SIMPLE_FLASH_COORD_OWNER_OTA);
    }
    
    return result;
}

/**
 * @brief Safe breakpoint information write with coordination
 */
Sota_FL_ResultType SOTA_FL_Simple_WriteBreakpointInfo(void)
{
    Sota_FL_ResultType result = SOTA_FL_FAILED;
    SimpleFlashCoord_ResultType coordResult;
    FlsLoader_ReturnType flsResult;
    uint32 databuff[4] = {SOTA_FL_NVM_BREAKPOINT_STATUS, SOTA_FL_NVM_BREAKPOINT_STATUS, 
                          SOTA_FL_NVM_BREAKPOINT_STATUS, SOTA_FL_NVM_BREAKPOINT_STATUS};
    
    if (!SOTA_FL_Simple_IsInitialized())
    {
        return SOTA_FL_FAILED;
    }
    
    /* Request DFlash access */
    coordResult = SimpleFlashCoord_RequestDFlashAccess(
        SIMPLE_FLASH_COORD_OWNER_OTA,
        SOTA_FL_NVM_ADDR_BREAKPOINT,
        SOTA_FL_SIMPLE_DEFAULT_TIMEOUT_MS
    );
    
    if (coordResult == SIMPLE_FLASH_COORD_OK)
    {
        /* Perform Flash write */
        flsResult = FlsLoader_Write(SOTA_FL_NVM_ADDR_BREAKPOINT, SOTA_FL_NVM_ONE_PAGES, (const uint8*)databuff);
        result = (flsResult == SOTA_FLSLOADER_E_OK) ? SOTA_FL_OK : SOTA_FL_FAILED;
        
        /* Release access */
        SimpleFlashCoord_ReleaseDFlashAccess(SIMPLE_FLASH_COORD_OWNER_OTA);
    }
    
    return result;
}

/***************************************************************************/
/*--------------------------Static Function Implementations---------------*/
/***************************************************************************/

/**
 * @brief Check if simple OTA Flash is initialized
 */
static boolean SOTA_FL_Simple_IsInitialized(void)
{
    return (SOTA_FL_SimpleControl.initPattern == SOTA_FL_SIMPLE_INIT_PATTERN);
}
