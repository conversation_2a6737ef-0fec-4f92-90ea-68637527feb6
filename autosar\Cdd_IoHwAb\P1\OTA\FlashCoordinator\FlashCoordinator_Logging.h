/*****************************************************************************
*
*   All Contents@CopyRight Of Neusoft Corporation
*
*   All rights reserved.
*
*   Filename: FlashCoordinator_Logging.h   
*
*   Description: Logging and monitoring system for FlashCoordinator
*                Provides comprehensive logging, debugging, and runtime
*                monitoring capabilities for Flash operations
*
*   Revision History:
*
*   Date            Author          Description
*   ----------      ----------      -----------------------------------------
*   2025-01-31      AI-Assistant    Initial created for logging and monitoring
*
*****************************************************************************/

#ifndef FLASH_COORDINATOR_LOGGING_H
#define FLASH_COORDINATOR_LOGGING_H

/***************************************************************************/
/*---------------------------------Includes--------------------------------*/
/***************************************************************************/
#include "Std_Types.h"
#include "FlashCoordinator.h"

/***************************************************************************/
/*----------------------------------Macros---------------------------------*/
/***************************************************************************/

/* Logging configuration */
#define FLASH_COORD_LOGGING_ENABLED               STD_ON
#define FLASH_COORD_DEBUG_LOGGING_ENABLED         STD_ON
#define FLASH_COORD_RUNTIME_MONITORING_ENABLED    STD_ON

/* Log levels */
#define FLASH_COORD_LOG_LEVEL_DEBUG               0u
#define FLASH_COORD_LOG_LEVEL_INFO                1u
#define FLASH_COORD_LOG_LEVEL_WARNING             2u
#define FLASH_COORD_LOG_LEVEL_ERROR               3u
#define FLASH_COORD_LOG_LEVEL_CRITICAL            4u

/* Log buffer configuration */
#define FLASH_COORD_LOG_BUFFER_SIZE               100u
#define FLASH_COORD_LOG_MESSAGE_MAX_LENGTH        128u

/* Performance monitoring thresholds */
#define FLASH_COORD_PERF_WARNING_THRESHOLD_MS     1000u
#define FLASH_COORD_PERF_CRITICAL_THRESHOLD_MS    5000u

/* Monitoring intervals */
#define FLASH_COORD_MONITOR_INTERVAL_MS           100u
#define FLASH_COORD_STATISTICS_UPDATE_INTERVAL_MS 1000u

/***************************************************************************/
/*----------------------------Type Definitions-----------------------------*/
/***************************************************************************/

/** Log levels */
typedef enum
{
    FLASH_COORD_LOG_DEBUG = 0u,             /**< Debug level */
    FLASH_COORD_LOG_INFO,                   /**< Information level */
    FLASH_COORD_LOG_WARNING,                /**< Warning level */
    FLASH_COORD_LOG_ERROR,                  /**< Error level */
    FLASH_COORD_LOG_CRITICAL                /**< Critical level */
} FlashCoord_LogLevelType;

/** Log entry structure */
typedef struct
{
    uint32 timestamp;                       /**< Log timestamp */
    FlashCoord_LogLevelType level;          /**< Log level */
    FlashCoord_OwnerType owner;             /**< Related owner */
    uint32 address;                         /**< Related address */
    uint32 errorCode;                       /**< Error code */
    char message[FLASH_COORD_LOG_MESSAGE_MAX_LENGTH]; /**< Log message */
} FlashCoord_LogEntryType;

/** Performance metrics */
typedef struct
{
    uint32 totalOperations;                 /**< Total operations */
    uint32 averageAccessTimeMs;             /**< Average access time */
    uint32 maxAccessTimeMs;                 /**< Maximum access time */
    uint32 minAccessTimeMs;                 /**< Minimum access time */
    uint32 timeoutCount;                    /**< Timeout count */
    uint32 errorCount;                      /**< Error count */
    uint32 warningCount;                    /**< Warning count */
} FlashCoord_PerformanceMetricsType;

/** Runtime monitoring data */
typedef struct
{
    boolean monitoringActive;               /**< Monitoring active flag */
    uint32 lastMonitorTime;                 /**< Last monitor timestamp */
    uint32 monitoringCycles;                /**< Monitoring cycle count */
    FlashCoord_PerformanceMetricsType metrics; /**< Performance metrics */
    uint32 systemLoadPercent;              /**< System load percentage */
    uint32 memoryUsageBytes;                /**< Memory usage in bytes */
} FlashCoord_RuntimeMonitorType;

/***************************************************************************/
/*--------------------External Function Declarations-----------------------*/
/***************************************************************************/

/**
 * @brief Initialize logging system
 * 
 * @details This function initializes the logging and monitoring
 *          system for FlashCoordinator.
 * 
 * @param[in] None
 * @return FlashCoord_ResultType
 * @retval FLASH_COORD_OK           Initialization successful
 * @retval FLASH_COORD_ERROR        Initialization failed
 */
FlashCoord_ResultType FlashCoord_Logging_Init(void);

/**
 * @brief Log a message
 * 
 * @details This function logs a message with specified level
 *          and context information.
 * 
 * @param[in] level                 Log level
 * @param[in] owner                 Related owner
 * @param[in] address               Related address
 * @param[in] errorCode             Error code
 * @param[in] message               Log message
 * @return FlashCoord_ResultType
 * @retval FLASH_COORD_OK           Message logged successfully
 * @retval FLASH_COORD_ERROR        Logging failed
 */
FlashCoord_ResultType FlashCoord_Logging_LogMessage(
    FlashCoord_LogLevelType level,
    FlashCoord_OwnerType owner,
    uint32 address,
    uint32 errorCode,
    const char* message
);

/**
 * @brief Log Flash access operation
 * 
 * @details This function logs a Flash access operation with
 *          performance metrics.
 * 
 * @param[in] owner                 Owner performing operation
 * @param[in] address               Target address
 * @param[in] length                Operation length
 * @param[in] accessTimeMs          Access time in milliseconds
 * @param[in] result                Operation result
 * @return FlashCoord_ResultType
 * @retval FLASH_COORD_OK           Operation logged successfully
 */
FlashCoord_ResultType FlashCoord_Logging_LogFlashAccess(
    FlashCoord_OwnerType owner,
    uint32 address,
    uint32 length,
    uint32 accessTimeMs,
    FlashCoord_ResultType result
);

/**
 * @brief Get log entries
 * 
 * @details This function retrieves log entries from the log buffer.
 * 
 * @param[out] entries              Pointer to log entries array
 * @param[in] maxEntries            Maximum number of entries to retrieve
 * @param[out] actualEntries        Actual number of entries retrieved
 * @return FlashCoord_ResultType
 * @retval FLASH_COORD_OK           Entries retrieved successfully
 * @retval FLASH_COORD_INVALID_PARAM Invalid parameter
 */
FlashCoord_ResultType FlashCoord_Logging_GetLogEntries(
    FlashCoord_LogEntryType* entries,
    uint32 maxEntries,
    uint32* actualEntries
);

/**
 * @brief Clear log buffer
 * 
 * @details This function clears all entries from the log buffer.
 * 
 * @param[in] None
 * @return FlashCoord_ResultType
 * @retval FLASH_COORD_OK           Log buffer cleared
 */
FlashCoord_ResultType FlashCoord_Logging_ClearLog(void);

/**
 * @brief Start runtime monitoring
 * 
 * @details This function starts runtime monitoring of
 *          FlashCoordinator performance and system metrics.
 * 
 * @param[in] None
 * @return FlashCoord_ResultType
 * @retval FLASH_COORD_OK           Monitoring started
 * @retval FLASH_COORD_ERROR        Failed to start monitoring
 */
FlashCoord_ResultType FlashCoord_Logging_StartMonitoring(void);

/**
 * @brief Stop runtime monitoring
 * 
 * @details This function stops runtime monitoring.
 * 
 * @param[in] None
 * @return FlashCoord_ResultType
 * @retval FLASH_COORD_OK           Monitoring stopped
 */
FlashCoord_ResultType FlashCoord_Logging_StopMonitoring(void);

/**
 * @brief Get performance metrics
 * 
 * @details This function retrieves current performance metrics.
 * 
 * @param[out] metrics              Pointer to metrics structure
 * @return FlashCoord_ResultType
 * @retval FLASH_COORD_OK           Metrics retrieved successfully
 * @retval FLASH_COORD_INVALID_PARAM Invalid parameter
 */
FlashCoord_ResultType FlashCoord_Logging_GetPerformanceMetrics(
    FlashCoord_PerformanceMetricsType* metrics
);

/**
 * @brief Reset performance metrics
 * 
 * @details This function resets all performance metrics.
 * 
 * @param[in] None
 * @return FlashCoord_ResultType
 * @retval FLASH_COORD_OK           Metrics reset successfully
 */
FlashCoord_ResultType FlashCoord_Logging_ResetPerformanceMetrics(void);

/**
 * @brief Set log level
 * 
 * @details This function sets the minimum log level for filtering.
 * 
 * @param[in] level                 Minimum log level
 * @return FlashCoord_ResultType
 * @retval FLASH_COORD_OK           Log level set successfully
 * @retval FLASH_COORD_INVALID_PARAM Invalid log level
 */
FlashCoord_ResultType FlashCoord_Logging_SetLogLevel(
    FlashCoord_LogLevelType level
);

/**
 * @brief Main function for logging and monitoring
 * 
 * @details This function handles periodic logging and monitoring tasks.
 * 
 * @param[in] None
 * @return None
 */
void FlashCoord_Logging_MainFunction(void);

/* Convenience macros for logging */
#if (FLASH_COORD_DEBUG_LOGGING_ENABLED == STD_ON)
#define FLASH_COORD_LOG_DEBUG(owner, addr, code, msg) \
    FlashCoord_Logging_LogMessage(FLASH_COORD_LOG_DEBUG, owner, addr, code, msg)
#define FLASH_COORD_LOG_INFO(owner, addr, code, msg) \
    FlashCoord_Logging_LogMessage(FLASH_COORD_LOG_INFO, owner, addr, code, msg)
#define FLASH_COORD_LOG_WARNING(owner, addr, code, msg) \
    FlashCoord_Logging_LogMessage(FLASH_COORD_LOG_WARNING, owner, addr, code, msg)
#define FLASH_COORD_LOG_ERROR(owner, addr, code, msg) \
    FlashCoord_Logging_LogMessage(FLASH_COORD_LOG_ERROR, owner, addr, code, msg)
#define FLASH_COORD_LOG_CRITICAL(owner, addr, code, msg) \
    FlashCoord_Logging_LogMessage(FLASH_COORD_LOG_CRITICAL, owner, addr, code, msg)
#else
#define FLASH_COORD_LOG_DEBUG(owner, addr, code, msg)
#define FLASH_COORD_LOG_INFO(owner, addr, code, msg)
#define FLASH_COORD_LOG_WARNING(owner, addr, code, msg)
#define FLASH_COORD_LOG_ERROR(owner, addr, code, msg)
#define FLASH_COORD_LOG_CRITICAL(owner, addr, code, msg)
#endif

#endif /* FLASH_COORDINATOR_LOGGING_H */
