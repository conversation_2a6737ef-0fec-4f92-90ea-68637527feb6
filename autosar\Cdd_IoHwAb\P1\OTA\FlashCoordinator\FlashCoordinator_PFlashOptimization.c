/*****************************************************************************
*
*   All Contents@CopyRight Of Neusoft Corporation
*
*   All rights reserved.
*
*   Filename: FlashCoordinator_PFlashOptimization.c   
*
*   Description: PFlash parallel access optimization implementation
*
*   Revision History:
*
*   Date            Author          Description
*   ----------      ----------      -----------------------------------------
*   2025-01-31      AI-Assistant    Initial created for PFlash optimization
*
*****************************************************************************/

/***************************************************************************/
/*---------------------------------Includes--------------------------------*/
/***************************************************************************/
#include "FlashCoordinator_PFlashOptimization.h"
#include "Det.h"

/***************************************************************************/
/*---------------------------------Macros----------------------------------*/
/***************************************************************************/

/* Module initialization pattern */
#define FLASH_COORD_PFLASH_INIT_PATTERN           0xABCDEF12uL

/* Performance thresholds */
#define FLASH_COORD_PFLASH_FAST_PATH_THRESHOLD_MS 10u
#define FLASH_COORD_PFLASH_COORDINATION_OVERHEAD_THRESHOLD_MS 5u

/***************************************************************************/
/*-----------------------------Type Definitions----------------------------*/
/***************************************************************************/

/** PFlash optimization control structure */
typedef struct
{
    uint32 initPattern;                     /**< Initialization pattern */
    boolean optimizationEnabled;            /**< Optimization enable flag */
    FlashCoord_ABSwapConfigType abSwapConfig; /**< AB swap configuration */
    FlashCoord_PFlashStatisticsType statistics; /**< Performance statistics */
    uint32 lastOptimizationTime;           /**< Last optimization timestamp */
} FlashCoord_PFlashControlType;

/***************************************************************************/
/*------------------------------Static Data--------------------------------*/
/***************************************************************************/

/** PFlash optimization control structure */
static FlashCoord_PFlashControlType FlashCoord_PFlashControl = {0};

/***************************************************************************/
/*--------------------------Static Function Declarations------------------*/
/***************************************************************************/

static boolean FlashCoord_PFlashOptimization_IsInitialized(void);
static uint32 FlashCoord_PFlashOptimization_GetCurrentTime(void);
static void FlashCoord_PFlashOptimization_UpdateStatistics(
    FlashCoord_PFlashAccessModeType mode,
    uint32 accessTime
);
static boolean FlashCoord_PFlashOptimization_IsABSwapConflict(
    uint32 address,
    FlashCoord_OwnerType owner
);

/***************************************************************************/
/*--------------------External Function Implementations--------------------*/
/***************************************************************************/

/**
 * @brief Initialize PFlash optimization module
 */
FlashCoord_ResultType FlashCoord_PFlashOptimization_Init(void)
{
    /* Initialize control structure */
    FlashCoord_PFlashControl.initPattern = FLASH_COORD_PFLASH_INIT_PATTERN;
    FlashCoord_PFlashControl.optimizationEnabled = TRUE;
    FlashCoord_PFlashControl.lastOptimizationTime = 0u;
    
    /* Initialize AB swap configuration */
    FlashCoord_PFlashControl.abSwapConfig.activeBank = FLASH_COORD_PFLASH_BANK_A;
    FlashCoord_PFlashControl.abSwapConfig.otaBank = FLASH_COORD_PFLASH_BANK_B;
    FlashCoord_PFlashControl.abSwapConfig.swapInProgress = FALSE;
    FlashCoord_PFlashControl.abSwapConfig.swapStartTime = 0u;
    
    /* Initialize statistics */
    FlashCoord_PFlashControl.statistics.totalPFlashOperations = 0u;
    FlashCoord_PFlashControl.statistics.bankAOperations = 0u;
    FlashCoord_PFlashControl.statistics.bankBOperations = 0u;
    FlashCoord_PFlashControl.statistics.parallelOperations = 0u;
    FlashCoord_PFlashControl.statistics.fastPathOperations = 0u;
    FlashCoord_PFlashControl.statistics.coordinatedOperations = 0u;
    FlashCoord_PFlashControl.statistics.maxParallelAccessTimeMs = 0u;
    FlashCoord_PFlashControl.statistics.averageParallelAccessTimeMs = 0u;
    FlashCoord_PFlashControl.statistics.coordinationOverheadMs = 0u;
    
    return FLASH_COORD_OK;
}

/**
 * @brief Determine optimal access mode for PFlash operation
 */
FlashCoord_PFlashAccessModeType FlashCoord_PFlashOptimization_GetOptimalMode(
    uint32 address,
    uint32 length,
    FlashCoord_OwnerType owner
)
{
    FlashCoord_PFlashAccessModeType mode = FLASH_COORD_PFLASH_MODE_COORDINATED;
    
    if (!FlashCoord_PFlashOptimization_IsInitialized())
    {
        return FLASH_COORD_PFLASH_MODE_COORDINATED;
    }
    
    /* Check if address is in PFlash range */
    if (!FlashCoord_PFlashOptimization_IsPFlashAddress(address))
    {
        return FLASH_COORD_PFLASH_MODE_COORDINATED;
    }
    
#if (FLASH_COORD_PFLASH_OPTIMIZATION_ENABLED == STD_ON)
    
    /* Check for AB swap conflicts */
    if (FlashCoord_PFlashOptimization_IsABSwapConflict(address, owner))
    {
        mode = FLASH_COORD_PFLASH_MODE_COORDINATED;
    }
    /* Check if parallel access is possible */
    else if (FlashCoord_PFlashOptimization_CanRunInParallel(address, owner))
    {
#if (FLASH_COORD_PFLASH_FAST_PATH_ENABLED == STD_ON)
        /* Use fast path for small operations */
        if (length <= FLASH_COORD_PFLASH_FAST_PATH_THRESHOLD_MS)
        {
            mode = FLASH_COORD_PFLASH_MODE_FAST_PATH;
        }
        else
#endif
        {
            mode = FLASH_COORD_PFLASH_MODE_DIRECT;
        }
    }
    else
    {
        mode = FLASH_COORD_PFLASH_MODE_COORDINATED;
    }
    
#endif /* FLASH_COORD_PFLASH_OPTIMIZATION_ENABLED */
    
    return mode;
}

/**
 * @brief Check if PFlash operation can run in parallel
 */
boolean FlashCoord_PFlashOptimization_CanRunInParallel(
    uint32 address,
    FlashCoord_OwnerType owner
)
{
    FlashCoord_PFlashBankType targetBank;
    boolean canRunInParallel = FALSE;
    
    if (!FlashCoord_PFlashOptimization_IsInitialized())
    {
        return FALSE;
    }
    
    /* Get target bank */
    targetBank = FlashCoord_PFlashOptimization_GetBank(address);
    
    if (targetBank == FLASH_COORD_PFLASH_BANK_UNKNOWN)
    {
        return FALSE;
    }
    
#if (FLASH_COORD_PFLASH_PARALLEL_ACCESS == STD_ON)
    
    /* Check AB swap configuration */
    if (!FlashCoord_PFlashControl.abSwapConfig.swapInProgress)
    {
        /* Different banks can operate in parallel */
        if (owner == FLASH_COORD_OWNER_OTA)
        {
            /* OTA should use the designated OTA bank */
            canRunInParallel = (targetBank == FlashCoord_PFlashControl.abSwapConfig.otaBank);
        }
        else
        {
            /* AUTOSAR modules should use the active bank */
            canRunInParallel = (targetBank == FlashCoord_PFlashControl.abSwapConfig.activeBank);
        }
    }
    else
    {
        /* During swap, coordination is required */
        canRunInParallel = FALSE;
    }
    
#endif /* FLASH_COORD_PFLASH_PARALLEL_ACCESS */
    
    return canRunInParallel;
}

/**
 * @brief Get PFlash bank from address
 */
FlashCoord_PFlashBankType FlashCoord_PFlashOptimization_GetBank(uint32 address)
{
    if (address >= FLASH_COORD_PFLASH_BANK_A_START && 
        address <= FLASH_COORD_PFLASH_BANK_A_END)
    {
        return FLASH_COORD_PFLASH_BANK_A;
    }
    else if (address >= FLASH_COORD_PFLASH_BANK_B_START && 
             address <= FLASH_COORD_PFLASH_BANK_B_END)
    {
        return FLASH_COORD_PFLASH_BANK_B;
    }
    
    return FLASH_COORD_PFLASH_BANK_UNKNOWN;
}

/**
 * @brief Configure AB swap settings
 */
FlashCoord_ResultType FlashCoord_PFlashOptimization_ConfigureABSwap(
    FlashCoord_PFlashBankType activeBank,
    FlashCoord_PFlashBankType otaBank
)
{
    if (!FlashCoord_PFlashOptimization_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }
    
    /* Validate parameters */
    if (activeBank == otaBank || 
        activeBank >= FLASH_COORD_PFLASH_BANK_UNKNOWN ||
        otaBank >= FLASH_COORD_PFLASH_BANK_UNKNOWN)
    {
        return FLASH_COORD_INVALID_PARAM;
    }
    
    /* Update configuration */
    FlashCoord_PFlashControl.abSwapConfig.activeBank = activeBank;
    FlashCoord_PFlashControl.abSwapConfig.otaBank = otaBank;
    
    return FLASH_COORD_OK;
}

/**
 * @brief Start AB swap operation
 */
FlashCoord_ResultType FlashCoord_PFlashOptimization_StartABSwap(void)
{
    if (!FlashCoord_PFlashOptimization_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }
    
    /* Check if swap is already in progress */
    if (FlashCoord_PFlashControl.abSwapConfig.swapInProgress)
    {
        return FLASH_COORD_BUSY;
    }
    
    /* Start swap operation */
    FlashCoord_PFlashControl.abSwapConfig.swapInProgress = TRUE;
    FlashCoord_PFlashControl.abSwapConfig.swapStartTime = 
        FlashCoord_PFlashOptimization_GetCurrentTime();
    
    return FLASH_COORD_OK;
}

/**
 * @brief Complete AB swap operation
 */
FlashCoord_ResultType FlashCoord_PFlashOptimization_CompleteABSwap(void)
{
    FlashCoord_PFlashBankType tempBank;
    
    if (!FlashCoord_PFlashOptimization_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }
    
    /* Check if swap is in progress */
    if (!FlashCoord_PFlashControl.abSwapConfig.swapInProgress)
    {
        return FLASH_COORD_ERROR;
    }
    
    /* Swap the banks */
    tempBank = FlashCoord_PFlashControl.abSwapConfig.activeBank;
    FlashCoord_PFlashControl.abSwapConfig.activeBank = 
        FlashCoord_PFlashControl.abSwapConfig.otaBank;
    FlashCoord_PFlashControl.abSwapConfig.otaBank = tempBank;
    
    /* Complete swap operation */
    FlashCoord_PFlashControl.abSwapConfig.swapInProgress = FALSE;
    FlashCoord_PFlashControl.abSwapConfig.swapStartTime = 0u;
    
    return FLASH_COORD_OK;
}

/**
 * @brief Get PFlash optimization statistics
 */
FlashCoord_ResultType FlashCoord_PFlashOptimization_GetStatistics(
    FlashCoord_PFlashStatisticsType* statistics
)
{
    if (statistics == NULL_PTR)
    {
        return FLASH_COORD_INVALID_PARAM;
    }
    
    if (!FlashCoord_PFlashOptimization_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }
    
    *statistics = FlashCoord_PFlashControl.statistics;
    
    return FLASH_COORD_OK;
}

/**
 * @brief Reset PFlash optimization statistics
 */
FlashCoord_ResultType FlashCoord_PFlashOptimization_ResetStatistics(void)
{
    if (!FlashCoord_PFlashOptimization_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }

    /* Reset all statistics */
    FlashCoord_PFlashControl.statistics.totalPFlashOperations = 0u;
    FlashCoord_PFlashControl.statistics.bankAOperations = 0u;
    FlashCoord_PFlashControl.statistics.bankBOperations = 0u;
    FlashCoord_PFlashControl.statistics.parallelOperations = 0u;
    FlashCoord_PFlashControl.statistics.fastPathOperations = 0u;
    FlashCoord_PFlashControl.statistics.coordinatedOperations = 0u;
    FlashCoord_PFlashControl.statistics.maxParallelAccessTimeMs = 0u;
    FlashCoord_PFlashControl.statistics.averageParallelAccessTimeMs = 0u;
    FlashCoord_PFlashControl.statistics.coordinationOverheadMs = 0u;

    return FLASH_COORD_OK;
}

/**
 * @brief Check if address is in PFlash range
 */
boolean FlashCoord_PFlashOptimization_IsPFlashAddress(uint32 address)
{
    return ((address >= FLASH_COORD_PFLASH_BANK_A_START &&
             address <= FLASH_COORD_PFLASH_BANK_A_END) ||
            (address >= FLASH_COORD_PFLASH_BANK_B_START &&
             address <= FLASH_COORD_PFLASH_BANK_B_END));
}

/**
 * @brief Optimize PFlash access path
 */
FlashCoord_ResultType FlashCoord_PFlashOptimization_OptimizeAccessPath(
    uint32 address,
    uint32 length,
    FlashCoord_OwnerType owner
)
{
    FlashCoord_PFlashAccessModeType optimalMode;
    FlashCoord_PFlashBankType targetBank;
    uint32 startTime;
    uint32 accessTime;

    if (!FlashCoord_PFlashOptimization_IsInitialized())
    {
        return FLASH_COORD_NOT_INITIALIZED;
    }

    startTime = FlashCoord_PFlashOptimization_GetCurrentTime();

    /* Determine optimal access mode */
    optimalMode = FlashCoord_PFlashOptimization_GetOptimalMode(address, length, owner);

    /* Update statistics based on chosen mode */
    targetBank = FlashCoord_PFlashOptimization_GetBank(address);

    FlashCoord_PFlashControl.statistics.totalPFlashOperations++;

    if (targetBank == FLASH_COORD_PFLASH_BANK_A)
    {
        FlashCoord_PFlashControl.statistics.bankAOperations++;
    }
    else if (targetBank == FLASH_COORD_PFLASH_BANK_B)
    {
        FlashCoord_PFlashControl.statistics.bankBOperations++;
    }

    accessTime = FlashCoord_PFlashOptimization_GetCurrentTime() - startTime;
    FlashCoord_PFlashOptimization_UpdateStatistics(optimalMode, accessTime);

    return FLASH_COORD_OK;
}

/**
 * @brief Main function for PFlash optimization
 */
void FlashCoord_PFlashOptimization_MainFunction(void)
{
    uint32 currentTime;

    if (!FlashCoord_PFlashOptimization_IsInitialized())
    {
        return;
    }

    currentTime = FlashCoord_PFlashOptimization_GetCurrentTime();

    /* Periodic optimization tasks */
    if (FlashCoord_PFlashControl.optimizationEnabled)
    {
        /* Check for long-running swap operations */
        if (FlashCoord_PFlashControl.abSwapConfig.swapInProgress)
        {
            uint32 swapDuration = currentTime - FlashCoord_PFlashControl.abSwapConfig.swapStartTime;

            /* If swap is taking too long, consider intervention */
            if (swapDuration > 30000u) /* 30 seconds threshold */
            {
                /* Log warning or take corrective action */
                /* This could indicate a problem with the swap process */
            }
        }

        FlashCoord_PFlashControl.lastOptimizationTime = currentTime;
    }
}

/***************************************************************************/
/*--------------------------Static Function Implementations---------------*/
/***************************************************************************/

/**
 * @brief Check if PFlash optimization is initialized
 */
static boolean FlashCoord_PFlashOptimization_IsInitialized(void)
{
    return (FlashCoord_PFlashControl.initPattern == FLASH_COORD_PFLASH_INIT_PATTERN);
}

/**
 * @brief Get current system time
 */
static uint32 FlashCoord_PFlashOptimization_GetCurrentTime(void)
{
    /* Implementation depends on available timer service */
    /* For now, use a simple counter - should be replaced with actual timer */
    static uint32 timeCounter = 0u;
    return ++timeCounter;
}

/**
 * @brief Update PFlash access statistics
 */
static void FlashCoord_PFlashOptimization_UpdateStatistics(
    FlashCoord_PFlashAccessModeType mode,
    uint32 accessTime
)
{
    switch (mode)
    {
        case FLASH_COORD_PFLASH_MODE_DIRECT:
            FlashCoord_PFlashControl.statistics.parallelOperations++;

            /* Update parallel access time statistics */
            if (accessTime > FlashCoord_PFlashControl.statistics.maxParallelAccessTimeMs)
            {
                FlashCoord_PFlashControl.statistics.maxParallelAccessTimeMs = accessTime;
            }

            /* Calculate average parallel access time */
            if (FlashCoord_PFlashControl.statistics.parallelOperations > 0u)
            {
                FlashCoord_PFlashControl.statistics.averageParallelAccessTimeMs =
                    ((FlashCoord_PFlashControl.statistics.averageParallelAccessTimeMs *
                      (FlashCoord_PFlashControl.statistics.parallelOperations - 1u)) + accessTime) /
                    FlashCoord_PFlashControl.statistics.parallelOperations;
            }
            break;

        case FLASH_COORD_PFLASH_MODE_FAST_PATH:
            FlashCoord_PFlashControl.statistics.fastPathOperations++;
            break;

        case FLASH_COORD_PFLASH_MODE_COORDINATED:
            FlashCoord_PFlashControl.statistics.coordinatedOperations++;

            /* Track coordination overhead */
            if (accessTime > FLASH_COORD_PFLASH_COORDINATION_OVERHEAD_THRESHOLD_MS)
            {
                FlashCoord_PFlashControl.statistics.coordinationOverheadMs +=
                    (accessTime - FLASH_COORD_PFLASH_COORDINATION_OVERHEAD_THRESHOLD_MS);
            }
            break;

        default:
            /* Unknown mode - no statistics update */
            break;
    }
}

/**
 * @brief Check for AB swap conflicts
 */
static boolean FlashCoord_PFlashOptimization_IsABSwapConflict(
    uint32 address,
    FlashCoord_OwnerType owner
)
{
    FlashCoord_PFlashBankType targetBank;

    /* Get target bank */
    targetBank = FlashCoord_PFlashOptimization_GetBank(address);

    if (targetBank == FLASH_COORD_PFLASH_BANK_UNKNOWN)
    {
        return FALSE;
    }

    /* Check for conflicts during swap operation */
    if (FlashCoord_PFlashControl.abSwapConfig.swapInProgress)
    {
        return TRUE; /* All operations require coordination during swap */
    }

    /* Check for cross-bank access conflicts */
    if (owner == FLASH_COORD_OWNER_OTA)
    {
        /* OTA should not access the active bank */
        return (targetBank == FlashCoord_PFlashControl.abSwapConfig.activeBank);
    }
    else
    {
        /* AUTOSAR modules should not access the OTA bank */
        return (targetBank == FlashCoord_PFlashControl.abSwapConfig.otaBank);
    }
}
