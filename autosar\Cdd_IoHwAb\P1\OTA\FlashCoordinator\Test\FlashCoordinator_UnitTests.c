/*****************************************************************************
*
*   All Contents@CopyRight Of Neusoft Corporation
*
*   All rights reserved.
*
*   Filename: FlashCoordinator_UnitTests.c   
*
*   Description: Unit test implementations for FlashCoordinator
*
*   Revision History:
*
*   Date            Author          Description
*   ----------      ----------      -----------------------------------------
*   2025-01-31      AI-Assistant    Initial created for unit tests
*
*****************************************************************************/

/***************************************************************************/
/*---------------------------------Includes--------------------------------*/
/***************************************************************************/
#include "FlashCoordinator_Test.h"

/***************************************************************************/
/*--------------------Unit Test Implementations----------------------------*/
/***************************************************************************/

/**
 * @brief Test FlashCoordinator basic initialization
 */
FlashCoord_TestResultType FlashCoord_Test_Init_Basic(void)
{
    FlashCoord_ResultType result;
    
    /* Test initialization */
    result = FlashCoord_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "FlashCoordinator initialization failed");
    
    /* Test double initialization */
    result = FlashCoord_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "FlashCoordinator double initialization failed");
    
    /* Test deinitialization */
    result = FlashCoord_DeInit();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "FlashCoordinator deinitialization failed");
    
    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test DFlash access request
 */
FlashCoord_TestResultType FlashCoord_Test_RequestAccess_DFlash(void)
{
    FlashCoord_ResultType result;
    
    /* Initialize FlashCoordinator */
    result = FlashCoord_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "FlashCoordinator initialization failed");
    
    /* Test valid DFlash access request */
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_OTA,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "DFlash access request failed");
    
    /* Test concurrent access request (should fail) */
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_NVM,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR,
        1024u,
        1000u
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_BUSY || result == FLASH_COORD_TIMEOUT, 
                           "Concurrent DFlash access should be denied");
    
    /* Release access */
    result = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_OTA);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "DFlash access release failed");
    
    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test PFlash access request
 */
FlashCoord_TestResultType FlashCoord_Test_RequestAccess_PFlash(void)
{
    FlashCoord_ResultType result;
    
    /* Initialize FlashCoordinator */
    result = FlashCoord_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "FlashCoordinator initialization failed");
    
    /* Test PFlash Bank A access */
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_SYSTEM,
        FLASH_COORD_MEM_TYPE_PFLASH_A,
        FLASH_COORD_TEST_PFLASH_A_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "PFlash Bank A access request failed");
    
    /* Test PFlash Bank B access (should succeed in parallel) */
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_OTA,
        FLASH_COORD_MEM_TYPE_PFLASH_B,
        FLASH_COORD_TEST_PFLASH_B_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "PFlash Bank B access request failed");
    
    /* Release both accesses */
    result = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_SYSTEM);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "PFlash Bank A access release failed");
    
    result = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_OTA);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "PFlash Bank B access release failed");
    
    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test valid access release
 */
FlashCoord_TestResultType FlashCoord_Test_ReleaseAccess_Valid(void)
{
    FlashCoord_ResultType result;
    
    /* Initialize FlashCoordinator */
    result = FlashCoord_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "FlashCoordinator initialization failed");
    
    /* Request access */
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_OTA,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Access request failed");
    
    /* Release access */
    result = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_OTA);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Access release failed");
    
    /* Verify access is released by requesting again */
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_NVM,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Access should be available after release");
    
    /* Clean up */
    result = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_NVM);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Cleanup release failed");
    
    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test invalid access release
 */
FlashCoord_TestResultType FlashCoord_Test_ReleaseAccess_Invalid(void)
{
    FlashCoord_ResultType result;
    
    /* Initialize FlashCoordinator */
    result = FlashCoord_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "FlashCoordinator initialization failed");
    
    /* Try to release access without requesting */
    result = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_OTA);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_ACCESS_DENIED, "Invalid release should be denied");
    
    /* Request access with one owner */
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_OTA,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Access request failed");
    
    /* Try to release with different owner */
    result = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_NVM);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_ACCESS_DENIED, "Wrong owner release should be denied");
    
    /* Clean up with correct owner */
    result = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_OTA);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Correct owner release failed");
    
    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test timeout handling
 */
FlashCoord_TestResultType FlashCoord_Test_Timeout_Handling(void)
{
    FlashCoord_ResultType result;
    
    /* Initialize FlashCoordinator */
    result = FlashCoord_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "FlashCoordinator initialization failed");
    
    /* Request access with first owner */
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_OTA,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "First access request failed");
    
    /* Request access with second owner (should timeout) */
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_NVM,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR,
        1024u,
        100u  /* Short timeout */
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_TIMEOUT || result == FLASH_COORD_BUSY, 
                           "Second access should timeout");
    
    /* Clean up */
    result = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_OTA);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Cleanup release failed");
    
    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test concurrent access scenarios
 */
FlashCoord_TestResultType FlashCoord_Test_Concurrent_Access(void)
{
    FlashCoord_ResultType result;
    
    /* Initialize FlashCoordinator */
    result = FlashCoord_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "FlashCoordinator initialization failed");
    
    /* Test concurrent DFlash access (should be serialized) */
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_OTA,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "First DFlash access failed");
    
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_NVM,
        FLASH_COORD_MEM_TYPE_DFLASH,
        FLASH_COORD_TEST_DFLASH_ADDR + 0x1000u,
        1024u,
        100u
    );
    FLASH_COORD_TEST_ASSERT(result != FLASH_COORD_OK, "Concurrent DFlash access should be denied");
    
    /* Release first access */
    result = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_OTA);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "First access release failed");
    
    /* Test concurrent PFlash access (should succeed) */
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_SYSTEM,
        FLASH_COORD_MEM_TYPE_PFLASH_A,
        FLASH_COORD_TEST_PFLASH_A_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "PFlash A access failed");
    
    result = FlashCoord_RequestAccess(
        FLASH_COORD_OWNER_OTA,
        FLASH_COORD_MEM_TYPE_PFLASH_B,
        FLASH_COORD_TEST_PFLASH_B_ADDR,
        1024u,
        5000u
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "PFlash B access failed");
    
    /* Clean up */
    result = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_SYSTEM);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "PFlash A release failed");
    
    result = FlashCoord_ReleaseAccess(FLASH_COORD_OWNER_OTA);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "PFlash B release failed");
    
    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test AUTOSAR integration initialization
 */
FlashCoord_TestResultType FlashCoord_Test_AutosarIntegration_Init(void)
{
    FlashCoord_ResultType result;

    /* Test AUTOSAR integration initialization */
    result = FlashCoord_AutosarIntegration_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "AUTOSAR integration initialization failed");

    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test AUTOSAR status check
 */
FlashCoord_TestResultType FlashCoord_Test_AutosarIntegration_StatusCheck(void)
{
    FlashCoord_AutosarCheckResultType result;
    FlashCoord_AutosarStatusType status;

    /* Initialize AUTOSAR integration */
    FlashCoord_AutosarIntegration_Init();

    /* Test status check */
    result = FlashCoord_AutosarIntegration_CheckAllIdle(1000u);
    FLASH_COORD_TEST_ASSERT(result != FLASH_COORD_AUTOSAR_STATUS_ERROR, "AUTOSAR status check failed");

    /* Test status retrieval */
    FlashCoord_ResultType getResult = FlashCoord_AutosarIntegration_GetStatus(&status);
    FLASH_COORD_TEST_ASSERT(getResult == FLASH_COORD_OK, "AUTOSAR status retrieval failed");

    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test error handling initialization
 */
FlashCoord_TestResultType FlashCoord_Test_ErrorHandling_Init(void)
{
    FlashCoord_ResultType result;

    /* Test error handling initialization */
    result = FlashCoord_ErrorHandling_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Error handling initialization failed");

    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test error reporting
 */
FlashCoord_TestResultType FlashCoord_Test_ErrorHandling_ReportError(void)
{
    FlashCoord_ResultType result;
    FlashCoord_ErrorInfoType errorInfo;

    /* Initialize error handling */
    result = FlashCoord_ErrorHandling_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Error handling initialization failed");

    /* Create test error */
    errorInfo.errorType = FLASH_COORD_ERROR_TYPE_TIMEOUT;
    errorInfo.severity = FLASH_COORD_ERROR_SEVERITY_WARNING;
    errorInfo.owner = FLASH_COORD_OWNER_OTA;
    errorInfo.errorCode = 12345u;
    errorInfo.timestamp = 0u;
    errorInfo.address = FLASH_COORD_TEST_DFLASH_ADDR;
    errorInfo.length = 1024u;
    errorInfo.description = "Test error message";

    /* Report error */
    result = FlashCoord_ErrorHandling_ReportError(&errorInfo);
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Error reporting failed");

    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test PFlash optimization initialization
 */
FlashCoord_TestResultType FlashCoord_Test_PFlashOptimization_Init(void)
{
    FlashCoord_ResultType result;

    /* Test PFlash optimization initialization */
    result = FlashCoord_PFlashOptimization_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "PFlash optimization initialization failed");

    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test PFlash parallel access
 */
FlashCoord_TestResultType FlashCoord_Test_PFlashOptimization_ParallelAccess(void)
{
    FlashCoord_ResultType result;
    boolean canRunInParallel;
    FlashCoord_PFlashBankType bank;

    /* Initialize PFlash optimization */
    result = FlashCoord_PFlashOptimization_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "PFlash optimization initialization failed");

    /* Test bank detection */
    bank = FlashCoord_PFlashOptimization_GetBank(FLASH_COORD_TEST_PFLASH_A_ADDR);
    FLASH_COORD_TEST_ASSERT(bank == FLASH_COORD_PFLASH_BANK_A, "PFlash Bank A detection failed");

    bank = FlashCoord_PFlashOptimization_GetBank(FLASH_COORD_TEST_PFLASH_B_ADDR);
    FLASH_COORD_TEST_ASSERT(bank == FLASH_COORD_PFLASH_BANK_B, "PFlash Bank B detection failed");

    /* Test parallel access capability */
    canRunInParallel = FlashCoord_PFlashOptimization_CanRunInParallel(
        FLASH_COORD_TEST_PFLASH_A_ADDR,
        FLASH_COORD_OWNER_SYSTEM
    );
    FLASH_COORD_TEST_ASSERT(canRunInParallel == TRUE, "PFlash parallel access check failed");

    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test logging initialization
 */
FlashCoord_TestResultType FlashCoord_Test_Logging_Init(void)
{
    FlashCoord_ResultType result;

    /* Test logging initialization */
    result = FlashCoord_Logging_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Logging initialization failed");

    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test message logging
 */
FlashCoord_TestResultType FlashCoord_Test_Logging_LogMessage(void)
{
    FlashCoord_ResultType result;

    /* Initialize logging */
    result = FlashCoord_Logging_Init();
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Logging initialization failed");

    /* Test message logging */
    result = FlashCoord_Logging_LogMessage(
        FLASH_COORD_LOG_INFO,
        FLASH_COORD_OWNER_OTA,
        FLASH_COORD_TEST_DFLASH_ADDR,
        0u,
        "Test log message"
    );
    FLASH_COORD_TEST_ASSERT(result == FLASH_COORD_OK, "Message logging failed");

    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test OTA safe access initialization
 */
FlashCoord_TestResultType FlashCoord_Test_OTA_SafeAccess_Init(void)
{
    Sota_FL_ResultType result;

    /* Test OTA safe access initialization */
    result = SOTA_FL_SafeAccess_Init();
    FLASH_COORD_TEST_ASSERT(result == SOTA_FL_OK, "OTA safe access initialization failed");

    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test OTA safe NVM data read
 */
FlashCoord_TestResultType FlashCoord_Test_OTA_SafeAccess_ReadNvmData(void)
{
    Sota_FL_ResultType result;
    uint32 data;

    /* Initialize OTA safe access */
    result = SOTA_FL_SafeAccess_Init();
    FLASH_COORD_TEST_ASSERT(result == SOTA_FL_OK, "OTA safe access initialization failed");

    /* Initialize FlashCoordinator */
    FlashCoord_Init();

    /* Test safe NVM data read */
    result = SOTA_FL_SafeAccess_ReadNvmData(FLASH_COORD_TEST_DFLASH_ADDR, &data);
    FLASH_COORD_TEST_ASSERT(result == SOTA_FL_OK, "OTA safe NVM data read failed");

    return FLASH_COORD_TEST_PASS;
}

/**
 * @brief Test OTA safe NVM data write
 */
FlashCoord_TestResultType FlashCoord_Test_OTA_SafeAccess_WriteNvmData(void)
{
    Sota_FL_ResultType result;

    /* Initialize OTA safe access */
    result = SOTA_FL_SafeAccess_Init();
    FLASH_COORD_TEST_ASSERT(result == SOTA_FL_OK, "OTA safe access initialization failed");

    /* Initialize FlashCoordinator */
    FlashCoord_Init();

    /* Test safe NVM data write */
    result = SOTA_FL_SafeAccess_WriteNvmData(FLASH_COORD_TEST_DFLASH_ADDR, FLASH_COORD_TEST_DATA_PATTERN_1);
    FLASH_COORD_TEST_ASSERT(result == SOTA_FL_OK, "OTA safe NVM data write failed");

    return FLASH_COORD_TEST_PASS;
}
