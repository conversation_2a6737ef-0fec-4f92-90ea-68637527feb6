/*****************************************************************************
*
*   All Contents@CopyRight Of Neusoft Corporation
*
*   All rights reserved.
*
*   Filename: SimpleFlashCoordinator_Test.c   
*
*   Description: Simple test cases for SimpleFlashCoordinator
*
*   Revision History:
*
*   Date            Author          Description
*   ----------      ----------      -----------------------------------------
*   2025-01-31      AI-Assistant    Initial created for simple testing
*
*****************************************************************************/

/***************************************************************************/
/*---------------------------------Includes--------------------------------*/
/***************************************************************************/
#include "SimpleFlashCoordinator.h"
#include "SOTA_FL_Simple.h"
#include <stdio.h>

/***************************************************************************/
/*----------------------------------Macros---------------------------------*/
/***************************************************************************/

#define SIMPLE_TEST_ASSERT(condition, message) \
    do { \
        if (!(condition)) { \
            printf("TEST FAILED: %s\n", message); \
            return FALSE; \
        } \
    } while(0)

#define SIMPLE_TEST_DFLASH_ADDR                   0xAF084000uL
#define SIMPLE_TEST_PFLASH_ADDR                   0xA0100000uL

/***************************************************************************/
/*--------------------Test Function Implementations------------------------*/
/***************************************************************************/

/**
 * @brief Test SimpleFlashCoordinator initialization
 */
boolean SimpleFlashCoordinator_Test_Init(void)
{
    SimpleFlashCoord_ResultType result;
    
    printf("Testing SimpleFlashCoordinator initialization...\n");
    
    /* Test initialization */
    result = SimpleFlashCoord_Init();
    SIMPLE_TEST_ASSERT(result == SIMPLE_FLASH_COORD_OK, "SimpleFlashCoordinator initialization failed");
    
    /* Test current owner (should be none) */
    SimpleFlashCoord_OwnerType owner = SimpleFlashCoord_GetCurrentOwner();
    SIMPLE_TEST_ASSERT(owner == SIMPLE_FLASH_COORD_OWNER_NONE, "Initial owner should be NONE");
    
    printf("SimpleFlashCoordinator initialization test PASSED\n");
    return TRUE;
}

/**
 * @brief Test DFlash access coordination
 */
boolean SimpleFlashCoordinator_Test_DFlashAccess(void)
{
    SimpleFlashCoord_ResultType result;
    
    printf("Testing DFlash access coordination...\n");
    
    /* Initialize coordinator */
    SimpleFlashCoord_Init();
    
    /* Test DFlash access request */
    result = SimpleFlashCoord_RequestDFlashAccess(
        SIMPLE_FLASH_COORD_OWNER_OTA,
        SIMPLE_TEST_DFLASH_ADDR,
        5000u
    );
    SIMPLE_TEST_ASSERT(result == SIMPLE_FLASH_COORD_OK, "DFlash access request failed");
    
    /* Test current owner */
    SimpleFlashCoord_OwnerType owner = SimpleFlashCoord_GetCurrentOwner();
    SIMPLE_TEST_ASSERT(owner == SIMPLE_FLASH_COORD_OWNER_OTA, "Current owner should be OTA");
    
    /* Test concurrent access (should fail) */
    result = SimpleFlashCoord_RequestDFlashAccess(
        SIMPLE_FLASH_COORD_OWNER_AUTOSAR,
        SIMPLE_TEST_DFLASH_ADDR,
        1000u
    );
    SIMPLE_TEST_ASSERT(result != SIMPLE_FLASH_COORD_OK, "Concurrent DFlash access should be denied");
    
    /* Test access release */
    result = SimpleFlashCoord_ReleaseDFlashAccess(SIMPLE_FLASH_COORD_OWNER_OTA);
    SIMPLE_TEST_ASSERT(result == SIMPLE_FLASH_COORD_OK, "DFlash access release failed");
    
    /* Test owner after release */
    owner = SimpleFlashCoord_GetCurrentOwner();
    SIMPLE_TEST_ASSERT(owner == SIMPLE_FLASH_COORD_OWNER_NONE, "Owner should be NONE after release");
    
    printf("DFlash access coordination test PASSED\n");
    return TRUE;
}

/**
 * @brief Test address coordination requirement
 */
boolean SimpleFlashCoordinator_Test_AddressCheck(void)
{
    boolean requiresCoordination;
    
    printf("Testing address coordination requirement...\n");
    
    /* Test DFlash address */
    requiresCoordination = SimpleFlashCoord_RequiresCoordination(SIMPLE_TEST_DFLASH_ADDR);
    SIMPLE_TEST_ASSERT(requiresCoordination == TRUE, "DFlash address should require coordination");
    
    /* Test PFlash address */
    requiresCoordination = SimpleFlashCoord_RequiresCoordination(SIMPLE_TEST_PFLASH_ADDR);
    SIMPLE_TEST_ASSERT(requiresCoordination == FALSE, "PFlash address should not require coordination");
    
    printf("Address coordination requirement test PASSED\n");
    return TRUE;
}

/**
 * @brief Test OTA simple functions
 */
boolean SimpleFlashCoordinator_Test_OTAFunctions(void)
{
    Sota_FL_ResultType result;
    uint32 data;
    
    printf("Testing OTA simple functions...\n");
    
    /* Initialize systems */
    SimpleFlashCoord_Init();
    result = SOTA_FL_Simple_Init();
    SIMPLE_TEST_ASSERT(result == SOTA_FL_OK, "SOTA_FL_Simple initialization failed");
    
    /* Test NVM data read */
    data = SOTA_FL_Simple_ReadNvmData(SIMPLE_TEST_DFLASH_ADDR);
    /* Note: This will return default value since we're not actually reading from Flash */
    SIMPLE_TEST_ASSERT(data == 0xFFFFFFFFuL, "NVM data read should return default value");
    
    /* Test PFlash read (no coordination) */
    data = SOTA_FL_Simple_ReadNvmData(SIMPLE_TEST_PFLASH_ADDR);
    /* This should work without coordination */
    
    /* Test data write status erase */
    result = SOTA_FL_Simple_EraseDataWrStatus();
    /* Note: This may fail in test environment without actual Flash hardware */
    /* The important thing is that coordination is attempted */
    
    /* Test ECU reset flag write */
    result = SOTA_FL_Simple_WriteEcuResetFlag();
    /* Note: This may fail in test environment without actual Flash hardware */
    
    printf("OTA simple functions test PASSED\n");
    return TRUE;
}

/**
 * @brief Test force release functionality
 */
boolean SimpleFlashCoordinator_Test_ForceRelease(void)
{
    SimpleFlashCoord_ResultType result;
    
    printf("Testing force release functionality...\n");
    
    /* Initialize coordinator */
    SimpleFlashCoord_Init();
    
    /* Request access */
    result = SimpleFlashCoord_RequestDFlashAccess(
        SIMPLE_FLASH_COORD_OWNER_OTA,
        SIMPLE_TEST_DFLASH_ADDR,
        5000u
    );
    SIMPLE_TEST_ASSERT(result == SIMPLE_FLASH_COORD_OK, "DFlash access request failed");
    
    /* Test force release */
    result = SimpleFlashCoord_ForceRelease();
    SIMPLE_TEST_ASSERT(result == SIMPLE_FLASH_COORD_OK, "Force release failed");
    
    /* Test owner after force release */
    SimpleFlashCoord_OwnerType owner = SimpleFlashCoord_GetCurrentOwner();
    SIMPLE_TEST_ASSERT(owner == SIMPLE_FLASH_COORD_OWNER_NONE, "Owner should be NONE after force release");
    
    printf("Force release functionality test PASSED\n");
    return TRUE;
}

/**
 * @brief Run all simple tests
 */
boolean SimpleFlashCoordinator_RunAllTests(void)
{
    boolean allPassed = TRUE;
    
    printf("=== SimpleFlashCoordinator Test Suite ===\n");
    
    /* Run all tests */
    if (!SimpleFlashCoordinator_Test_Init()) allPassed = FALSE;
    if (!SimpleFlashCoordinator_Test_DFlashAccess()) allPassed = FALSE;
    if (!SimpleFlashCoordinator_Test_AddressCheck()) allPassed = FALSE;
    if (!SimpleFlashCoordinator_Test_OTAFunctions()) allPassed = FALSE;
    if (!SimpleFlashCoordinator_Test_ForceRelease()) allPassed = FALSE;
    
    /* Print summary */
    printf("=== Test Summary ===\n");
    if (allPassed)
    {
        printf("All tests PASSED!\n");
    }
    else
    {
        printf("Some tests FAILED!\n");
    }
    
    return allPassed;
}

/**
 * @brief Main test function
 */
int main(void)
{
    boolean testResult = SimpleFlashCoordinator_RunAllTests();
    return testResult ? 0 : 1;
}
