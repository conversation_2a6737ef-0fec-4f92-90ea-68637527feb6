/*****************************************************************************
*
*   All Contents@CopyRight Of Neusoft Corporation
*
*   All rights reserved.
*
*   Filename: SimpleFlashCoordinator.h   
*
*   Description: Simplified Flash Coordinator for DFlash access conflict resolution
*                Minimal implementation focused only on DFlash coordination
*                between OTA and AUTOSAR modules
*
*   Revision History:
*
*   Date            Author          Description
*   ----------      ----------      -----------------------------------------
*   2025-01-31      AI-Assistant    Initial created for simplified coordination
*
*****************************************************************************/

#ifndef SIMPLE_FLASH_COORDINATOR_H
#define SIMPLE_FLASH_COORDINATOR_H

/***************************************************************************/
/*---------------------------------Includes--------------------------------*/
/***************************************************************************/
#include "Std_Types.h"
#include "NvM.h"
#include "Fee.h"
#include "Fls_17_Dmu.h"
#include "MemIf.h"

/***************************************************************************/
/*----------------------------------Macros---------------------------------*/
/***************************************************************************/

/* DFlash address ranges that require coordination */
#define SIMPLE_FLASH_COORD_DFLASH0_START          0xAF000000uL
#define SIMPLE_FLASH_COORD_DFLASH0_END            0xAF0FFFFFuL
#define SIMPLE_FLASH_COORD_DFLASH1_START          0xAFC00000uL
#define SIMPLE_FLASH_COORD_DFLASH1_END            0xAFC1FFFFuL

/* Default timeout for DFlash access */
#define SIMPLE_FLASH_COORD_DEFAULT_TIMEOUT_MS     5000u

/* Maximum retry attempts */
#define SIMPLE_FLASH_COORD_MAX_RETRIES            3u

/***************************************************************************/
/*----------------------------Type Definitions-----------------------------*/
/***************************************************************************/

/** Simple Flash Coordinator result types */
typedef enum
{
    SIMPLE_FLASH_COORD_OK = 0u,             /**< Operation successful */
    SIMPLE_FLASH_COORD_BUSY,                /**< Flash is busy */
    SIMPLE_FLASH_COORD_TIMEOUT,             /**< Operation timed out */
    SIMPLE_FLASH_COORD_ERROR                /**< Operation failed */
} SimpleFlashCoord_ResultType;

/** Flash owner types */
typedef enum
{
    SIMPLE_FLASH_COORD_OWNER_NONE = 0u,     /**< No owner */
    SIMPLE_FLASH_COORD_OWNER_OTA,           /**< OTA component */
    SIMPLE_FLASH_COORD_OWNER_AUTOSAR        /**< AUTOSAR modules (NVM/FEE/FLS) */
} SimpleFlashCoord_OwnerType;

/***************************************************************************/
/*--------------------External Function Declarations-----------------------*/
/***************************************************************************/

/**
 * @brief Initialize Simple Flash Coordinator
 * 
 * @details Initializes the coordination mechanism for DFlash access
 * 
 * @param[in] None
 * @return SimpleFlashCoord_ResultType
 * @retval SIMPLE_FLASH_COORD_OK    Initialization successful
 * @retval SIMPLE_FLASH_COORD_ERROR Initialization failed
 */
SimpleFlashCoord_ResultType SimpleFlashCoord_Init(void);

/**
 * @brief Request DFlash access
 * 
 * @details Requests exclusive access to DFlash for the specified owner.
 *          Checks AUTOSAR module status and implements timeout protection.
 * 
 * @param[in] owner         Owner requesting access
 * @param[in] address       DFlash address to access
 * @param[in] timeoutMs     Timeout in milliseconds
 * @return SimpleFlashCoord_ResultType
 * @retval SIMPLE_FLASH_COORD_OK      Access granted
 * @retval SIMPLE_FLASH_COORD_BUSY    Flash is busy
 * @retval SIMPLE_FLASH_COORD_TIMEOUT Request timed out
 * @retval SIMPLE_FLASH_COORD_ERROR   Request failed
 */
SimpleFlashCoord_ResultType SimpleFlashCoord_RequestDFlashAccess(
    SimpleFlashCoord_OwnerType owner,
    uint32 address,
    uint32 timeoutMs
);

/**
 * @brief Release DFlash access
 * 
 * @details Releases DFlash access for the specified owner
 * 
 * @param[in] owner         Owner releasing access
 * @return SimpleFlashCoord_ResultType
 * @retval SIMPLE_FLASH_COORD_OK    Access released successfully
 * @retval SIMPLE_FLASH_COORD_ERROR Release failed
 */
SimpleFlashCoord_ResultType SimpleFlashCoord_ReleaseDFlashAccess(
    SimpleFlashCoord_OwnerType owner
);

/**
 * @brief Check if DFlash is accessible
 * 
 * @details Checks if DFlash is currently accessible for the specified owner
 * 
 * @param[in] owner         Owner to check
 * @return boolean
 * @retval TRUE             DFlash is accessible
 * @retval FALSE            DFlash is not accessible
 */
boolean SimpleFlashCoord_IsDFlashAccessible(SimpleFlashCoord_OwnerType owner);

/**
 * @brief Check if address requires coordination
 * 
 * @details Determines if the given address is in DFlash range and requires coordination
 * 
 * @param[in] address       Address to check
 * @return boolean
 * @retval TRUE             Address requires coordination
 * @retval FALSE            Address does not require coordination (PFlash)
 */
boolean SimpleFlashCoord_RequiresCoordination(uint32 address);

/**
 * @brief Get current DFlash owner
 * 
 * @details Returns the current owner of DFlash access
 * 
 * @param[in] None
 * @return SimpleFlashCoord_OwnerType Current owner
 */
SimpleFlashCoord_OwnerType SimpleFlashCoord_GetCurrentOwner(void);

/**
 * @brief Force release DFlash access (emergency function)
 * 
 * @details Forcefully releases DFlash access in case of timeout or error
 * 
 * @param[in] None
 * @return SimpleFlashCoord_ResultType
 * @retval SIMPLE_FLASH_COORD_OK    Force release successful
 */
SimpleFlashCoord_ResultType SimpleFlashCoord_ForceRelease(void);

#endif /* SIMPLE_FLASH_COORDINATOR_H */
